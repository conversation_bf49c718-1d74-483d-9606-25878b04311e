"""
Rate limiting utilities for MCP tools to prevent abuse.
"""

import time
import async<PERSON>
from typing import Dict, Optional, Any
from functools import wraps
from collections import defaultdict, deque
import threading


class RateLimiter:
    """Rate limiter using sliding window algorithm."""
    
    def __init__(self, max_requests: int = 10, window_seconds: int = 60):
        """
        Initialize rate limiter.
        
        Args:
            max_requests: Maximum requests allowed in the window
            window_seconds: Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.lock = threading.Lock()
    
    def is_allowed(self, client_id: str) -> bool:
        """
        Check if request is allowed for client.
        
        Args:
            client_id: Unique identifier for the client
            
        Returns:
            True if request is allowed, False otherwise
        """
        with self.lock:
            now = time.time()
            client_requests = self.requests[client_id]
            
            # Remove old requests outside the window
            while client_requests and client_requests[0] < now - self.window_seconds:
                client_requests.popleft()
            
            # Check if we're within the limit
            if len(client_requests) < self.max_requests:
                client_requests.append(now)
                return True
            
            return False
    
    def get_reset_time(self, client_id: str) -> float:
        """
        Get time until rate limit resets for client.
        
        Args:
            client_id: Unique identifier for the client
            
        Returns:
            Seconds until rate limit resets
        """
        with self.lock:
            client_requests = self.requests[client_id]
            if not client_requests:
                return 0.0
            
            oldest_request = client_requests[0]
            return max(0.0, oldest_request + self.window_seconds - time.time())


class GlobalRateLimiter:
    """Global rate limiter for all requests."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        Initialize global rate limiter.
        
        Args:
            max_requests: Maximum total requests allowed in the window
            window_seconds: Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: deque = deque()
        self.lock = threading.Lock()
    
    def is_allowed(self) -> bool:
        """
        Check if request is allowed globally.
        
        Returns:
            True if request is allowed, False otherwise
        """
        with self.lock:
            now = time.time()
            
            # Remove old requests outside the window
            while self.requests and self.requests[0] < now - self.window_seconds:
                self.requests.popleft()
            
            # Check if we're within the limit
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True
            
            return False


# Global rate limiter instances
# Per-client rate limiter (10 requests per minute)
client_rate_limiter = RateLimiter(max_requests=10, window_seconds=60)

# Global rate limiter (100 requests per minute across all clients)
global_rate_limiter = GlobalRateLimiter(max_requests=100, window_seconds=60)

# Tool-specific rate limiters
crawl_rate_limiter = RateLimiter(max_requests=5, window_seconds=60)  # Crawling is expensive
query_rate_limiter = RateLimiter(max_requests=20, window_seconds=60)  # Queries are cheaper


def rate_limit(limiter: RateLimiter, client_id_func: Optional[callable] = None):
    """
    Rate limiting decorator for MCP tools.
    
    Args:
        limiter: Rate limiter instance to use
        client_id_func: Function to extract client ID from arguments
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract client ID (use IP or session ID in real implementation)
            client_id = "default"
            if client_id_func:
                client_id = client_id_func(*args, **kwargs)
            
            # Check global rate limit first
            if not global_rate_limiter.is_allowed():
                import json
                return json.dumps({
                    "success": False,
                    "error": "Global rate limit exceeded. Please try again later.",
                    "retry_after": 60
                }, indent=2)
            
            # Check client-specific rate limit
            if not limiter.is_allowed(client_id):
                import json
                reset_time = limiter.get_reset_time(client_id)
                return json.dumps({
                    "success": False,
                    "error": "Rate limit exceeded. Please try again later.",
                    "retry_after": int(reset_time) + 1
                }, indent=2)
            
            # Execute the function
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def get_client_id_from_context(ctx, *args, **kwargs):
    """
    Extract client ID from MCP context.
    
    Args:
        ctx: MCP context object
        *args: Function arguments
        **kwargs: Function keyword arguments
        
    Returns:
        Client identifier string
    """
    # In a real implementation, you would extract this from the request context
    # For now, use a default client ID
    try:
        # Try to get some identifying information from the context
        if hasattr(ctx, 'request_context') and hasattr(ctx.request_context, 'client_info'):
            return str(ctx.request_context.client_info.get('id', 'default'))
        return "default"
    except:
        return "default"


# Tool-specific rate limiting decorators
def crawl_rate_limit(func):
    """Rate limit decorator for crawling tools."""
    return rate_limit(crawl_rate_limiter, get_client_id_from_context)(func)


def query_rate_limit(func):
    """Rate limit decorator for query tools."""
    return rate_limit(query_rate_limiter, get_client_id_from_context)(func)


def general_rate_limit(func):
    """General rate limit decorator for other tools."""
    return rate_limit(client_rate_limiter, get_client_id_from_context)(func)


# Async semaphore for concurrent request limiting
class ConcurrencyLimiter:
    """Limit concurrent requests to prevent resource exhaustion."""
    
    def __init__(self, max_concurrent: int = 10):
        """
        Initialize concurrency limiter.
        
        Args:
            max_concurrent: Maximum concurrent requests allowed
        """
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    def __call__(self, func):
        """Decorator to limit concurrent execution."""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async with self.semaphore:
                return await func(*args, **kwargs)
        return wrapper


# Global concurrency limiters
crawl_concurrency_limiter = ConcurrencyLimiter(max_concurrent=5)  # Limit crawling concurrency
query_concurrency_limiter = ConcurrencyLimiter(max_concurrent=20)  # Limit query concurrency
general_concurrency_limiter = ConcurrencyLimiter(max_concurrent=10)  # General concurrency limit