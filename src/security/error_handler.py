"""
Secure error handling to prevent information disclosure.
"""

import logging
import json
import time
import traceback
from typing import Dict, Any, Optional
from functools import wraps
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SecurityError(Exception):
    """Base class for security-related errors."""
    pass


class ValidationError(SecurityError):
    """Input validation error."""
    pass


class RateLimitError(SecurityError):
    """Rate limit exceeded error."""
    pass


class AuthenticationError(SecurityError):
    """Authentication error."""
    pass


class SecureErrorHandler:
    """Secure error handler that prevents information disclosure."""
    
    def __init__(self, debug_mode: bool = False):
        """
        Initialize secure error handler.
        
        Args:
            debug_mode: If True, include detailed error information
        """
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Handle error securely, preventing information disclosure.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            
        Returns:
            Sanitized error response
        """
        error_id = self._generate_error_id()
        severity = self._classify_error(error)
        
        # Log the full error details server-side
        self._log_error(error, error_id, context, severity)
        
        # Return sanitized error to client
        return self._create_client_response(error, error_id, severity)
    
    def _generate_error_id(self) -> str:
        """Generate unique error ID for tracking."""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _classify_error(self, error: Exception) -> ErrorSeverity:
        """Classify error severity."""
        if isinstance(error, (ValidationError, ValueError, TypeError)):
            return ErrorSeverity.LOW
        elif isinstance(error, (RateLimitError, PermissionError)):
            return ErrorSeverity.MEDIUM
        elif isinstance(error, (AuthenticationError, SecurityError)):
            return ErrorSeverity.HIGH
        elif isinstance(error, (KeyError, AttributeError, ImportError)):
            return ErrorSeverity.CRITICAL
        else:
            return ErrorSeverity.MEDIUM
    
    def _log_error(self, error: Exception, error_id: str, context: Dict[str, Any], severity: ErrorSeverity):
        """Log error details server-side."""
        log_data = {
            "error_id": error_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "severity": severity.value,
            "context": context or {},
            "traceback": traceback.format_exc() if self.debug_mode else None
        }
        
        # Log at appropriate level based on severity
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"Critical error {error_id}: {log_data}")
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(f"High severity error {error_id}: {log_data}")
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"Medium severity error {error_id}: {log_data}")
        else:
            self.logger.info(f"Low severity error {error_id}: {log_data}")
    
    def _create_client_response(self, error: Exception, error_id: str, severity: ErrorSeverity) -> Dict[str, Any]:
        """Create sanitized client response."""
        # Generic error messages that don't reveal system internals
        generic_messages = {
            ErrorSeverity.LOW: "Invalid input provided. Please check your parameters.",
            ErrorSeverity.MEDIUM: "Request could not be processed. Please try again.",
            ErrorSeverity.HIGH: "Access denied. Please check your permissions.",
            ErrorSeverity.CRITICAL: "Internal server error. Please contact support."
        }
        
        response = {
            "success": False,
            "error": generic_messages.get(severity, "An error occurred"),
            "error_id": error_id,
            "severity": severity.value
        }
        
        # Include specific error details only for certain safe error types
        if isinstance(error, ValidationError):
            response["error"] = str(error)
        elif isinstance(error, RateLimitError):
            response["error"] = "Rate limit exceeded. Please try again later."
        elif isinstance(error, AuthenticationError):
            response["error"] = "Authentication failed. Please check your credentials."
        
        # Include detailed information only in debug mode
        if self.debug_mode:
            response["debug_info"] = {
                "error_type": type(error).__name__,
                "error_message": str(error),
                "traceback": traceback.format_exc()
            }
        
        return response


# Global error handler instance
error_handler = SecureErrorHandler(debug_mode=False)


def secure_error_handler(func):
    """
    Decorator to wrap functions with secure error handling.
    
    Args:
        func: Function to wrap
        
    Returns:
        Wrapped function with secure error handling
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # Extract context information
            context = {
                "function": func.__name__,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys())
            }
            
            # Handle error securely
            error_response = error_handler.handle_error(e, context)
            
            # Return JSON response
            return json.dumps(error_response, indent=2)
    
    return wrapper


def validate_and_sanitize_input(data: Any, field_name: str, max_length: int = 1000) -> Any:
    """
    Validate and sanitize input data.
    
    Args:
        data: Input data to validate
        field_name: Name of the field for error messages
        max_length: Maximum allowed length for string inputs
        
    Returns:
        Sanitized input data
        
    Raises:
        ValidationError: If input is invalid
    """
    if data is None:
        raise ValidationError(f"{field_name} cannot be None")
    
    if isinstance(data, str):
        if len(data) > max_length:
            raise ValidationError(f"{field_name} exceeds maximum length of {max_length}")
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
        for char in dangerous_chars:
            if char in data:
                raise ValidationError(f"{field_name} contains invalid characters")
        
        return data.strip()
    
    elif isinstance(data, (int, float)):
        if not isinstance(data, (int, float)) or data != data:  # Check for NaN
            raise ValidationError(f"{field_name} must be a valid number")
        return data
    
    elif isinstance(data, bool):
        return data
    
    elif isinstance(data, (list, dict)):
        # Recursively validate nested structures
        if isinstance(data, list):
            return [validate_and_sanitize_input(item, f"{field_name}[{i}]", max_length) 
                    for i, item in enumerate(data)]
        else:
            return {k: validate_and_sanitize_input(v, f"{field_name}.{k}", max_length) 
                    for k, v in data.items()}
    
    else:
        raise ValidationError(f"{field_name} has unsupported type: {type(data)}")


def sanitize_error_message(message: str) -> str:
    """
    Sanitize error message to prevent information disclosure.
    
    Args:
        message: Original error message
        
    Returns:
        Sanitized error message
    """
    # Remove file paths
    import re
    message = re.sub(r'/[^\s]*', '[PATH]', message)
    
    # Remove IP addresses
    message = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '[IP]', message)
    
    # Remove potentially sensitive patterns
    sensitive_patterns = [
        r'password[^\s]*',
        r'token[^\s]*',
        r'key[^\s]*',
        r'secret[^\s]*',
        r'api[_-]?key[^\s]*',
    ]
    
    for pattern in sensitive_patterns:
        message = re.sub(pattern, '[REDACTED]', message, flags=re.IGNORECASE)
    
    # Truncate if too long
    if len(message) > 200:
        message = message[:197] + "..."
    
    return message


def log_security_event(event_type: str, details: Dict[str, Any], severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """
    Log security events for monitoring.
    
    Args:
        event_type: Type of security event
        details: Event details
        severity: Event severity
    """
    logger = logging.getLogger("security")
    
    log_entry = {
        "event_type": event_type,
        "severity": severity.value,
        "details": details,
        "timestamp": time.time()
    }
    
    if severity == ErrorSeverity.CRITICAL:
        logger.critical(f"Security event: {log_entry}")
    elif severity == ErrorSeverity.HIGH:
        logger.error(f"Security event: {log_entry}")
    elif severity == ErrorSeverity.MEDIUM:
        logger.warning(f"Security event: {log_entry}")
    else:
        logger.info(f"Security event: {log_entry}")


# Configure logging
def setup_security_logging():
    """Setup security logging configuration."""
    import time
    
    # Create security logger
    security_logger = logging.getLogger("security")
    security_logger.setLevel(logging.INFO)
    
    # Create file handler for security events
    try:
        from pathlib import Path
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        handler = logging.FileHandler(log_dir / "security.log")
        handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        security_logger.addHandler(handler)
    except Exception as e:
        print(f"Failed to setup security logging: {e}")


# Initialize logging
setup_security_logging()