"""
Utility functions for the Crawl4AI MCP server.
"""
import os
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
import json
from supabase import create_client, Client
from urllib.parse import urlparse
import openai
import re
import time
import psutil
import gc
import asyncio
import threading

# Import performance optimizations
try:
    from performance.cache_manager import embedding_cache, crawl_cache, query_cache
    from performance.query_optimizer import get_query_optimizer, initialize_query_optimizer
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = True
except ImportError:
    print("Performance optimizations not available - using standard implementations")
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = False
    embedding_cache = None
    query_cache = None

# Load OpenAI API key for embeddings
openai.api_key = os.getenv("OPENAI_API_KEY")

# Memory monitoring and optimization utilities
class MemoryMonitor:
    """Monitor and manage memory usage to prevent leaks."""
    
    def __init__(self, warning_threshold_percent: float = 80.0, critical_threshold_percent: float = 90.0):
        self.warning_threshold = warning_threshold_percent
        self.critical_threshold = critical_threshold_percent
        self.process = psutil.Process()
        self.lock = threading.Lock()
        
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory usage information."""
        with self.lock:
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            system_memory = psutil.virtual_memory()
            
            return {
                "rss_mb": memory_info.rss / (1024 * 1024),  # Resident Set Size in MB
                "vms_mb": memory_info.vms / (1024 * 1024),  # Virtual Memory Size in MB
                "percent": memory_percent,
                "system_available_mb": system_memory.available / (1024 * 1024),
                "system_percent": system_memory.percent,
                "warning_threshold": self.warning_threshold,
                "critical_threshold": self.critical_threshold
            }
    
    def is_memory_warning(self) -> bool:
        """Check if memory usage is at warning level."""
        return self.process.memory_percent() >= self.warning_threshold
    
    def is_memory_critical(self) -> bool:
        """Check if memory usage is at critical level."""
        return self.process.memory_percent() >= self.critical_threshold
    
    def force_cleanup(self):
        """Force garbage collection and memory cleanup."""
        with self.lock:
            gc.collect()
            # Force multiple rounds of garbage collection
            for _ in range(3):
                gc.collect()
            
            print(f"Memory cleanup completed. Current usage: {self.process.memory_percent():.1f}%")
    
    def log_memory_status(self, context: str = ""):
        """Log current memory status."""
        info = self.get_memory_info()
        status = "NORMAL"
        if info["percent"] >= self.critical_threshold:
            status = "CRITICAL"
        elif info["percent"] >= self.warning_threshold:
            status = "WARNING"
        
        print(f"[{status}] Memory {context}: {info['percent']:.1f}% "
              f"({info['rss_mb']:.1f} MB RSS, {info['vms_mb']:.1f} MB VMS)")
        
        if status != "NORMAL":
            print(f"System memory: {info['system_percent']:.1f}% used, "
                  f"{info['system_available_mb']:.1f} MB available")

# Global memory monitor instance
memory_monitor = MemoryMonitor(warning_threshold_percent=75.0, critical_threshold_percent=85.0)

# Connection pooling for database operations
class DatabaseConnectionPool:
    """Simple connection pool for database operations."""
    
    def __init__(self, max_connections: int = 5):
        self.max_connections = max_connections
        self.connections = []
        self.lock = asyncio.Lock()
        self._url = os.getenv("SUPABASE_URL")
        self._key = os.getenv("SUPABASE_SERVICE_KEY")
        
    async def get_connection(self) -> Client:
        """Get a database connection from the pool."""
        async with self.lock:
            if self.connections:
                return self.connections.pop()
            else:
                # Create new connection
                if not self._url or not self._key:
                    raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
                return create_client(self._url, self._key)
    
    async def return_connection(self, connection: Client):
        """Return a connection to the pool."""
        async with self.lock:
            if len(self.connections) < self.max_connections:
                self.connections.append(connection)
            # If pool is full, connection will be garbage collected
    
    async def cleanup_all(self):
        """Clean up all connections in the pool."""
        async with self.lock:
            self.connections.clear()
            gc.collect()

# Global database connection pool
db_pool = DatabaseConnectionPool(max_connections=3)

# Simple job queue system for long-running operations
class JobQueue:
    """Simple async job queue for long-running crawl operations."""
    
    def __init__(self, max_concurrent_jobs: int = 2):
        self.max_concurrent_jobs = max_concurrent_jobs
        self.semaphore = asyncio.Semaphore(max_concurrent_jobs)
        self.active_jobs = {}
        self.lock = asyncio.Lock()
        
    async def submit_job(self, job_id: str, job_func, *args, **kwargs):
        """Submit a job to the queue."""
        async with self.lock:
            if job_id in self.active_jobs:
                raise ValueError(f"Job {job_id} is already running")
            
            self.active_jobs[job_id] = {
                "status": "queued",
                "start_time": time.time(),
                "progress": 0.0
            }
        
        # Start the job
        asyncio.create_task(self._run_job(job_id, job_func, *args, **kwargs))
        return job_id
    
    async def _run_job(self, job_id: str, job_func, *args, **kwargs):
        """Run a job with the semaphore limiting concurrency."""
        async with self.semaphore:
            try:
                async with self.lock:
                    if job_id in self.active_jobs:
                        self.active_jobs[job_id]["status"] = "running"
                
                # Monitor memory before job
                memory_monitor.log_memory_status(f"Starting job {job_id}")
                
                # Run the job
                result = await job_func(*args, **kwargs)
                
                async with self.lock:
                    if job_id in self.active_jobs:
                        self.active_jobs[job_id].update({
                            "status": "completed",
                            "result": result,
                            "end_time": time.time()
                        })
                
                # Monitor memory after job
                memory_monitor.log_memory_status(f"Completed job {job_id}")
                
                # Force cleanup if memory is high
                if memory_monitor.is_memory_warning():
                    memory_monitor.force_cleanup()
                    
            except Exception as e:
                async with self.lock:
                    if job_id in self.active_jobs:
                        self.active_jobs[job_id].update({
                            "status": "failed",
                            "error": str(e),
                            "end_time": time.time()
                        })
                
                print(f"Job {job_id} failed: {e}")
                
                # Force cleanup on error
                memory_monitor.force_cleanup()
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get the status of a job."""
        async with self.lock:
            return self.active_jobs.get(job_id, {"status": "not_found"})
    
    async def cleanup_completed_jobs(self):
        """Clean up completed jobs from memory."""
        async with self.lock:
            completed_jobs = [
                job_id for job_id, job in self.active_jobs.items()
                if job["status"] in ["completed", "failed"]
                and time.time() - job.get("end_time", 0) > 300  # 5 minutes old
            ]
            
            for job_id in completed_jobs:
                del self.active_jobs[job_id]
                
            if completed_jobs:
                print(f"Cleaned up {len(completed_jobs)} completed jobs")
                gc.collect()

# Global job queue
job_queue = JobQueue(max_concurrent_jobs=2)

def get_supabase_client() -> Client:
    """
    Get a Supabase client with the URL and key from environment variables.
    
    Returns:
        Supabase client instance
    """
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables")
    
    return create_client(url, key)

async def create_embeddings_batch_optimized(texts: List[str]) -> List[List[float]]:
    """
    Create embeddings for multiple texts with intelligent caching and optimization.
    
    Args:
        texts: List of texts to create embeddings for
        
    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    if not texts:
        return []
    
    # Use caching if available
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE and embedding_cache:
        cached_embeddings = []
        uncached_texts = []
        uncached_indices = []
        
        # Check cache for each text
        for i, text in enumerate(texts):
            cached_embedding = await embedding_cache.get_embedding(text)
            if cached_embedding is not None:
                cached_embeddings.append((i, cached_embedding))
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # Create embeddings for uncached texts
        if uncached_texts:
            new_embeddings = create_embeddings_batch(uncached_texts)
            
            # Cache the new embeddings
            for j, embedding in enumerate(new_embeddings):
                text = uncached_texts[j]
                await embedding_cache.set_embedding(text, embedding)
            
            # Combine cached and new embeddings
            result = [None] * len(texts)
            
            # Place cached embeddings
            for i, embedding in cached_embeddings:
                result[i] = embedding
            
            # Place new embeddings
            for j, embedding in enumerate(new_embeddings):
                result[uncached_indices[j]] = embedding
            
            return result
        else:
            # All embeddings were cached
            result = [None] * len(texts)
            for i, embedding in cached_embeddings:
                result[i] = embedding
            return result
    else:
        # Fallback to standard implementation
        return create_embeddings_batch(texts)


def create_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """
    Create embeddings for multiple texts in a single API call (original implementation).
    
    Args:
        texts: List of texts to create embeddings for
        
    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    if not texts:
        return []
    
    max_retries = 3
    retry_delay = 1.0  # Start with 1 second delay
    
    for retry in range(max_retries):
        try:
            response = openai.embeddings.create(
                model="text-embedding-3-small", # Hardcoding embedding model for now, will change this later to be more dynamic
                input=texts
            )
            return [item.embedding for item in response.data]
        except Exception as e:
            if retry < max_retries - 1:
                print(f"Error creating batch embeddings (attempt {retry + 1}/{max_retries}): {e}")
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                print(f"Failed to create batch embeddings after {max_retries} attempts: {e}")
                # Try creating embeddings one by one as fallback
                print("Attempting to create embeddings individually...")
                embeddings = []
                successful_count = 0
                
                for i, text in enumerate(texts):
                    try:
                        individual_response = openai.embeddings.create(
                            model="text-embedding-3-small",
                            input=[text]
                        )
                        embeddings.append(individual_response.data[0].embedding)
                        successful_count += 1
                    except Exception as individual_error:
                        print(f"Failed to create embedding for text {i}: {individual_error}")
                        # Add zero embedding as fallback
                        embeddings.append([0.0] * 1536)
                
                print(f"Successfully created {successful_count}/{len(texts)} embeddings individually")
                return embeddings

async def create_embedding_optimized(text: str) -> List[float]:
    """
    Create an embedding for a single text with caching optimization.
    
    Args:
        text: Text to create an embedding for
        
    Returns:
        List of floats representing the embedding
    """
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE and embedding_cache:
        # Check cache first
        cached_embedding = await embedding_cache.get_embedding(text)
        if cached_embedding is not None:
            return cached_embedding
        
        # Create new embedding
        try:
            embeddings = create_embeddings_batch([text])
            embedding = embeddings[0] if embeddings else [0.0] * 1536
            
            # Cache the result
            await embedding_cache.set_embedding(text, embedding)
            return embedding
        except Exception as e:
            print(f"Error creating embedding: {e}")
            return [0.0] * 1536
    else:
        # Fallback to standard implementation
        return create_embedding(text)


def create_embedding(text: str) -> List[float]:
    """
    Create an embedding for a single text using OpenAI's API (original implementation).
    
    Args:
        text: Text to create an embedding for
        
    Returns:
        List of floats representing the embedding
    """
    try:
        embeddings = create_embeddings_batch([text])
        return embeddings[0] if embeddings else [0.0] * 1536
    except Exception as e:
        print(f"Error creating embedding: {e}")
        # Return empty embedding if there's an error
        return [0.0] * 1536

def generate_contextual_embedding(full_document: str, chunk: str) -> Tuple[str, bool]:
    """
    Generate contextual information for a chunk within a document to improve retrieval.
    
    Args:
        full_document: The complete document text
        chunk: The specific chunk of text to generate context for
        
    Returns:
        Tuple containing:
        - The contextual text that situates the chunk within the document
        - Boolean indicating if contextual embedding was performed
    """
    model_choice = os.getenv("MODEL_CHOICE")
    
    try:
        # Create the prompt for generating contextual information
        prompt = f"""<document> 
{full_document[:25000]} 
</document>
Here is the chunk we want to situate within the whole document 
<chunk> 
{chunk}
</chunk> 
Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk. Answer only with the succinct context and nothing else."""

        # Call the OpenAI API to generate contextual information
        response = openai.chat.completions.create(
            model=model_choice,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides concise contextual information."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=200
        )
        
        # Extract the generated context
        context = response.choices[0].message.content.strip()
        
        # Combine the context with the original chunk
        contextual_text = f"{context}\n---\n{chunk}"
        
        return contextual_text, True
    
    except Exception as e:
        print(f"Error generating contextual embedding: {e}. Using original chunk instead.")
        return chunk, False

def process_chunk_with_context(args):
    """
    Process a single chunk with contextual embedding.
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (url, content, full_document)
        
    Returns:
        Tuple containing:
        - The contextual text that situates the chunk within the document
        - Boolean indicating if contextual embedding was performed
    """
    url, content, full_document = args
    return generate_contextual_embedding(full_document, content)

async def add_documents_to_supabase_optimized(
    urls: List[str], 
    chunk_numbers: List[int],
    contents: List[str], 
    metadatas: List[Dict[str, Any]],
    url_to_full_document: Dict[str, str],
    batch_size: int = 20
) -> None:
    """
    Add documents to Supabase with performance optimizations.
    
    Args:
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        contents: List of document contents
        metadatas: List of document metadata
        url_to_full_document: Dictionary mapping URLs to their full document content
        batch_size: Size of each batch for insertion
    """
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
        try:
            # Use optimized batch operations
            optimizer = get_query_optimizer()
            
            # Get unique URLs to delete existing records
            unique_urls = list(set(urls))
            
            # Check if contextual embeddings are enabled
            use_contextual_embeddings = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false") == "true"
            
            # Process in batches
            for i in range(0, len(contents), batch_size):
                batch_end = min(i + batch_size, len(contents))
                
                # Get batch slices
                batch_urls = urls[i:batch_end]
                batch_chunk_numbers = chunk_numbers[i:batch_end]
                batch_contents = contents[i:batch_end]
                batch_metadatas = metadatas[i:batch_end]
                
                # Apply contextual embedding if enabled
                if use_contextual_embeddings:
                    contextual_contents = await _process_contextual_embeddings(
                        batch_contents, batch_urls, url_to_full_document, batch_metadatas
                    )
                else:
                    contextual_contents = batch_contents
                
                # Create embeddings using optimized batch creation
                batch_embeddings = await create_embeddings_batch_optimized(contextual_contents)
                
                # Prepare batch data
                batch_data = []
                for j in range(len(contextual_contents)):
                    parsed_url = urlparse(batch_urls[j])
                    source_id = parsed_url.netloc or parsed_url.path
                    
                    batch_data.append({
                        "url": batch_urls[j],
                        "chunk_number": batch_chunk_numbers[j],
                        "content": contextual_contents[j],
                        "metadata": {
                            "chunk_size": len(contextual_contents[j]),
                            **batch_metadatas[j]
                        },
                        "source_id": source_id,
                        "embedding": batch_embeddings[j]
                    })
                
                # Queue batch operation
                await optimizer.queue_batch_operation(
                    'insert_pages',
                    {
                        'data': batch_data,
                        'urls_to_delete': unique_urls if i == 0 else []  # Only delete on first batch
                    },
                    batch_size=50
                )
            
            return
            
        except Exception as e:
            print(f"Error in optimized document insertion: {e}")
            # Fallback to standard implementation
    
    # Standard implementation
    client = get_supabase_client()
    add_documents_to_supabase(
        client, urls, chunk_numbers, contents, metadatas, url_to_full_document, batch_size
    )


async def add_documents_to_supabase(
    client: Client, 
    urls: List[str], 
    chunk_numbers: List[int],
    contents: List[str], 
    metadatas: List[Dict[str, Any]],
    url_to_full_document: Dict[str, str],
    batch_size: int = 20
) -> None:
    """
    Add documents to the Supabase crawled_pages table in batches.
    Deletes existing records with the same URLs before inserting to prevent duplicates.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        contents: List of document contents
        metadatas: List of document metadata
        url_to_full_document: Dictionary mapping URLs to their full document content
        batch_size: Size of each batch for insertion
    """
    # Get unique URLs to delete existing records
    unique_urls = list(set(urls))
    
    # Delete existing records for these URLs in a single operation
    try:
        if unique_urls:
            # Use the .in_() filter to delete all records with matching URLs
            client.table("crawled_pages").delete().in_("url", unique_urls).execute()
    except Exception as e:
        print(f"Batch delete failed: {e}. Trying one-by-one deletion as fallback.")
        # Fallback: delete records one by one
        for url in unique_urls:
            try:
                client.table("crawled_pages").delete().eq("url", url).execute()
            except Exception as inner_e:
                print(f"Error deleting record for URL {url}: {inner_e}")
                # Continue with the next URL even if one fails
    
    # Check if MODEL_CHOICE is set for contextual embeddings
    use_contextual_embeddings = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false") == "true"
    print(f"\n\nUse contextual embeddings: {use_contextual_embeddings}\n\n")
    
    # Monitor memory at start
    memory_monitor.log_memory_status("before document processing")
    
    # Process in batches to avoid memory issues
    for i in range(0, len(contents), batch_size):
        batch_end = min(i + batch_size, len(contents))
        
        # Get batch slices
        batch_urls = urls[i:batch_end]
        batch_chunk_numbers = chunk_numbers[i:batch_end]
        batch_contents = contents[i:batch_end]
        batch_metadatas = metadatas[i:batch_end]
        
        # Apply contextual embedding to each chunk if MODEL_CHOICE is set
        if use_contextual_embeddings:
            contextual_contents = await _process_contextual_embeddings(
                batch_contents, batch_urls, url_to_full_document, batch_metadatas
            )
        else:
            # If not using contextual embeddings, use original contents
            contextual_contents = batch_contents
        
        # Create embeddings for the entire batch at once
        batch_embeddings = create_embeddings_batch(contextual_contents)
        
        batch_data = []
        for j in range(len(contextual_contents)):
            # Extract metadata fields
            chunk_size = len(contextual_contents[j])
            
            # Extract source_id from URL
            parsed_url = urlparse(batch_urls[j])
            source_id = parsed_url.netloc or parsed_url.path
            
            # Prepare data for insertion
            data = {
                "url": batch_urls[j],
                "chunk_number": batch_chunk_numbers[j],
                "content": contextual_contents[j],  # Store original content
                "metadata": {
                    "chunk_size": chunk_size,
                    **batch_metadatas[j]
                },
                "source_id": source_id,  # Add source_id field
                "embedding": batch_embeddings[j]  # Use embedding from contextual content
            }
            
            batch_data.append(data)
        
        # Insert batch into Supabase with retry logic
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table("crawled_pages").insert(batch_data).execute()
                # Success - break out of retry loop
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("Attempting to insert records individually...")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table("crawled_pages").insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"Failed to insert individual record for URL {record['url']}: {individual_error}")
                    
                    if successful_inserts > 0:
                        print(f"Successfully inserted {successful_inserts}/{len(batch_data)} records individually")
        
        # Monitor memory after each batch and force cleanup if critical
        if memory_monitor.is_memory_critical():
            print("Memory usage critical - forcing cleanup")
            memory_monitor.force_cleanup()
    
    # Monitor memory at end
    memory_monitor.log_memory_status("after document processing")
    
    # Force cleanup if memory is high
    if memory_monitor.is_memory_warning():
        memory_monitor.force_cleanup()

async def search_documents_optimized(
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Search for documents using optimized vector similarity with caching.
    
    Args:
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        
    Returns:
        List of matching documents
    """
    # Use optimized embedding creation
    query_embedding = await create_embedding_optimized(query)
    
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
        try:
            optimizer = get_query_optimizer()
            result = await optimizer.execute_optimized_query(
                'search_documents',
                {
                    'query_embedding': query_embedding,
                    'match_count': match_count,
                    'filter_metadata': filter_metadata
                },
                cache_ttl=300  # Cache for 5 minutes
            )
            return result.data
        except Exception as e:
            print(f"Error in optimized search: {e}")
            # Fallback to standard implementation
            client = get_supabase_client()
            return search_documents(client, query, match_count, filter_metadata)
    else:
        # Standard implementation
        client = get_supabase_client()
        return search_documents(client, query, match_count, filter_metadata)


def search_documents(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Search for documents in Supabase using vector similarity (original implementation).
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        
    Returns:
        List of matching documents
    """
    # Create embedding for the query
    query_embedding = create_embedding(query)
    
    # Execute the search using the match_crawled_pages function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata  # Pass the dictionary directly, not JSON-encoded
        
        result = client.rpc('match_crawled_pages', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching documents: {e}")
        return []


def extract_code_blocks(markdown_content: str, min_length: int = 50) -> List[Dict[str, Any]]:
    """
    Extract code blocks from markdown content along with context.
    
    Args:
        markdown_content: The markdown content to extract code blocks from
        min_length: Minimum length of code blocks to extract (default: 1000 characters)
        
    Returns:
        List of dictionaries containing code blocks and their context
    """
    code_blocks = []
    
    # Skip if content starts with triple backticks (edge case for files wrapped in backticks)
    content = markdown_content.strip()
    start_offset = 0
    if content.startswith('```'):
        # Skip the first triple backticks
        start_offset = 3
        print("Skipping initial triple backticks")
    
    # Find all occurrences of triple backticks
    backtick_positions = []
    pos = start_offset
    while True:
        pos = markdown_content.find('```', pos)
        if pos == -1:
            break
        backtick_positions.append(pos)
        pos += 3
    
    # Process pairs of backticks
    i = 0
    while i < len(backtick_positions) - 1:
        start_pos = backtick_positions[i]
        end_pos = backtick_positions[i + 1]
        
        # Extract the content between backticks
        code_section = markdown_content[start_pos+3:end_pos]
        
        # Check if there's a language specifier on the first line
        lines = code_section.split('\n', 1)
        if len(lines) > 1:
            # Check if first line is a language specifier (no spaces, common language names)
            first_line = lines[0].strip()
            if first_line and not ' ' in first_line and len(first_line) < 20:
                language = first_line
                code_content = lines[1].strip() if len(lines) > 1 else ""
            else:
                language = ""
                code_content = code_section.strip()
        else:
            language = ""
            code_content = code_section.strip()
        
        # Skip if code block is too short, but be more flexible with small but meaningful code
        if len(code_content) < min_length:
            # Allow shorter code blocks if they contain meaningful patterns
            if not _is_meaningful_code_snippet(code_content):
                i += 2  # Move to next pair
                continue
        
        # Extract context before (1000 chars)
        context_start = max(0, start_pos - 1000)
        context_before = markdown_content[context_start:start_pos].strip()
        
        # Extract context after (1000 chars)
        context_end = min(len(markdown_content), end_pos + 3 + 1000)
        context_after = markdown_content[end_pos + 3:context_end].strip()
        
        code_blocks.append({
            'code': code_content,
            'language': language,
            'context_before': context_before,
            'context_after': context_after,
            'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}"
        })
        
        # Move to next pair (skip the closing backtick we just processed)
        i += 2
    
    return code_blocks


def generate_code_example_summary(code: str, context_before: str, context_after: str) -> str:
    """
    Generate a summary for a code example using its surrounding context.
    
    Args:
        code: The code example
        context_before: Context before the code
        context_after: Context after the code
        
    Returns:
        A summary of what the code example demonstrates
    """
    model_choice = os.getenv("MODEL_CHOICE")
    
    # Create the prompt
    prompt = f"""<context_before>
{context_before[-500:] if len(context_before) > 500 else context_before}
</context_before>

<code_example>
{code[:1500] if len(code) > 1500 else code}
</code_example>

<context_after>
{context_after[:500] if len(context_after) > 500 else context_after}
</context_after>

Based on the code example and its surrounding context, provide a concise summary (2-3 sentences) that describes what this code example demonstrates and its purpose. Focus on the practical application and key concepts illustrated.
"""
    
    try:
        response = openai.chat.completions.create(
            model=model_choice,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides concise code example summaries."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=100
        )
        
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        print(f"Error generating code example summary: {e}")
        return "Code example for demonstration purposes."


def add_code_examples_to_supabase(
    client: Client,
    urls: List[str],
    chunk_numbers: List[int],
    code_examples: List[str],
    summaries: List[str],
    metadatas: List[Dict[str, Any]],
    batch_size: int = 20
):
    """
    Add code examples to the Supabase code_examples table in batches.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        code_examples: List of code example contents
        summaries: List of code example summaries
        metadatas: List of metadata dictionaries
        batch_size: Size of each batch for insertion
    """
    if not urls:
        return
        
    # Delete existing records for these URLs
    unique_urls = list(set(urls))
    for url in unique_urls:
        try:
            client.table('code_examples').delete().eq('url', url).execute()
        except Exception as e:
            print(f"Error deleting existing code examples for {url}: {e}")
    
    # Process in batches
    total_items = len(urls)
    for i in range(0, total_items, batch_size):
        batch_end = min(i + batch_size, total_items)
        batch_texts = []
        
        # Create combined texts for embedding (code + summary)
        for j in range(i, batch_end):
            combined_text = f"{code_examples[j]}\n\nSummary: {summaries[j]}"
            batch_texts.append(combined_text)
        
        # Create embeddings for the batch
        embeddings = create_embeddings_batch(batch_texts)
        
        # Check if embeddings are valid (not all zeros)
        valid_embeddings = []
        for embedding in embeddings:
            if embedding and not all(v == 0.0 for v in embedding):
                valid_embeddings.append(embedding)
            else:
                print(f"Warning: Zero or invalid embedding detected, creating new one...")
                # Try to create a single embedding as fallback
                single_embedding = create_embedding(batch_texts[len(valid_embeddings)])
                valid_embeddings.append(single_embedding)
        
        # Prepare batch data
        batch_data = []
        for j, embedding in enumerate(valid_embeddings):
            idx = i + j
            
            # Extract source_id from URL
            parsed_url = urlparse(urls[idx])
            source_id = parsed_url.netloc or parsed_url.path
            
            batch_data.append({
                'url': urls[idx],
                'chunk_number': chunk_numbers[idx],
                'content': code_examples[idx],
                'summary': summaries[idx],
                'metadata': metadatas[idx],  # Store as JSON object, not string
                'source_id': source_id,
                'embedding': embedding
            })
        
        # Insert batch into Supabase with retry logic
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table('code_examples').insert(batch_data).execute()
                # Success - break out of retry loop
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("Attempting to insert records individually...")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table('code_examples').insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"Failed to insert individual record for URL {record['url']}: {individual_error}")
                    
                    if successful_inserts > 0:
                        print(f"Successfully inserted {successful_inserts}/{len(batch_data)} records individually")
        print(f"Inserted batch {i//batch_size + 1} of {(total_items + batch_size - 1)//batch_size} code examples")


def update_source_info(client: Client, source_id: str, summary: str, word_count: int):
    """
    Update or insert source information in the sources table.
    
    Args:
        client: Supabase client
        source_id: The source ID (domain)
        summary: Summary of the source
        word_count: Total word count for the source
    """
    try:
        # Try to update existing source
        result = client.table('sources').update({
            'summary': summary,
            'total_word_count': word_count,
            'updated_at': 'now()'
        }).eq('source_id', source_id).execute()
        
        # If no rows were updated, insert new source
        if not result.data:
            client.table('sources').insert({
                'source_id': source_id,
                'summary': summary,
                'total_word_count': word_count
            }).execute()
            print(f"Created new source: {source_id}")
        else:
            print(f"Updated source: {source_id}")
            
    except Exception as e:
        print(f"Error updating source {source_id}: {e}")


def extract_source_summary(source_id: str, content: str, max_length: int = 500) -> str:
    """
    Extract a summary for a source from its content using an LLM.
    
    This function uses the OpenAI API to generate a concise summary of the source content.
    
    Args:
        source_id: The source ID (domain)
        content: The content to extract a summary from
        max_length: Maximum length of the summary
        
    Returns:
        A summary string
    """
    # Default summary if we can't extract anything meaningful
    default_summary = f"Content from {source_id}"
    
    if not content or len(content.strip()) == 0:
        return default_summary
    
    # Get the model choice from environment variables
    model_choice = os.getenv("MODEL_CHOICE")
    
    # Limit content length to avoid token limits
    truncated_content = content[:25000] if len(content) > 25000 else content
    
    # Create the prompt for generating the summary
    prompt = f"""<source_content>
{truncated_content}
</source_content>

The above content is from the documentation for '{source_id}'. Please provide a concise summary (3-5 sentences) that describes what this library/tool/framework is about. The summary should help understand what the library/tool/framework accomplishes and the purpose.
"""
    
    try:
        # Call the OpenAI API to generate the summary
        response = openai.chat.completions.create(
            model=model_choice,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides concise library/tool/framework summaries."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=150
        )
        
        # Extract the generated summary
        summary = response.choices[0].message.content.strip()
        
        # Ensure the summary is not too long
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
            
        return summary
    
    except Exception as e:
        print(f"Error generating summary with LLM for {source_id}: {e}. Using default summary.")
        return default_summary


async def search_code_examples_optimized(
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None,
    source_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for code examples using optimized vector similarity with caching.
    
    Args:
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        source_id: Optional source ID to filter results
        
    Returns:
        List of matching code examples
    """
    # Enhance query with technical context and synonyms for better matching
    enhanced_query = _enhance_code_query(query)
    
    # Use optimized embedding creation
    query_embedding = await create_embedding_optimized(enhanced_query)
    
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
        try:
            optimizer = get_query_optimizer()
            result = await optimizer.execute_optimized_query(
                'search_code_examples',
                {
                    'query_embedding': query_embedding,
                    'match_count': match_count,
                    'filter_metadata': filter_metadata,
                    'source_id': source_id
                },
                cache_ttl=300  # Cache for 5 minutes
            )
            return result.data
        except Exception as e:
            print(f"Error in optimized code search: {e}")
            # Fallback to standard implementation
            client = get_supabase_client()
            return search_code_examples(client, query, match_count, filter_metadata, source_id)
    else:
        # Standard implementation
        client = get_supabase_client()
        return search_code_examples(client, query, match_count, filter_metadata, source_id)


def search_code_examples(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None,
    source_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for code examples in Supabase using vector similarity (original implementation).
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        source_id: Optional source ID to filter results
        
    Returns:
        List of matching code examples
    """
    # Enhance query with technical context and synonyms for better matching
    enhanced_query = _enhance_code_query(query)
    
    # Create embedding for the enhanced query
    query_embedding = create_embedding(enhanced_query)
    
    # Execute the search using the match_code_examples function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata
            
        # Add source filter if provided
        if source_id:
            params['source_filter'] = source_id
        
        result = client.rpc('match_code_examples', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching code examples: {e}")
        return []


def _enhance_code_query(query: str) -> str:
    """
    Enhance a code search query with technical context and synonyms for better matching.
    
    Args:
        query: Original search query
        
    Returns:
        Enhanced query with technical context
    """
    # Technical keyword mappings for better search
    technical_mappings = {
        'api': ['REST API', 'endpoint', 'HTTP request', 'web service', 'API call'],
        'database': ['SQL', 'query', 'CRUD', 'ORM', 'database connection'],
        'auth': ['authentication', 'authorization', 'login', 'JWT', 'OAuth', 'session'],
        'async': ['asynchronous', 'await', 'Promise', 'concurrent', 'parallel'],
        'test': ['unit test', 'testing', 'mock', 'pytest', 'jest', 'TDD'],
        'config': ['configuration', 'settings', 'environment', 'ENV', 'dotenv'],
        'error': ['exception', 'error handling', 'try catch', 'debugging'],
        'web': ['HTTP', 'web server', 'Flask', 'Django', 'Express', 'web framework'],
        'data': ['JSON', 'XML', 'CSV', 'parsing', 'serialization', 'data processing'],
        'file': ['file I/O', 'read file', 'write file', 'file handling', 'filesystem'],
        'network': ['HTTP client', 'requests', 'networking', 'socket', 'API client'],
        'security': ['encryption', 'hashing', 'validation', 'sanitization', 'HTTPS'],
        'performance': ['optimization', 'caching', 'profiling', 'benchmarking'],
        'ui': ['user interface', 'frontend', 'React', 'Vue', 'Angular', 'component'],
        'ml': ['machine learning', 'AI', 'neural network', 'training', 'prediction'],
        'docker': ['containerization', 'deployment', 'DevOps', 'CI/CD'],
        'logging': ['log', 'debug', 'monitoring', 'observability', 'telemetry']
    }
    
    # Programming language specific terms
    language_context = {
        'python': ['import', 'def', 'class', 'pip', 'virtual environment', 'Django', 'Flask'],
        'javascript': ['function', 'const', 'let', 'npm', 'node.js', 'React', 'Vue'],
        'java': ['class', 'method', 'Spring', 'Maven', 'Gradle', 'JVM'],
        'go': ['func', 'package', 'goroutine', 'channel', 'go mod'],
        'rust': ['fn', 'struct', 'cargo', 'trait', 'ownership', 'borrow'],
        'typescript': ['interface', 'type', 'generic', 'decorator', 'npm'],
        'sql': ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'JOIN', 'INDEX'],
        'bash': ['shell script', 'command line', 'pipe', 'grep', 'awk'],
        'docker': ['dockerfile', 'container', 'image', 'compose', 'kubernetes'],
        'git': ['version control', 'commit', 'branch', 'merge', 'pull request']
    }
    
    query_lower = query.lower()
    enhanced_terms = []
    
    # Add technical context based on keywords
    for keyword, synonyms in technical_mappings.items():
        if keyword in query_lower:
            enhanced_terms.extend(synonyms[:3])  # Add top 3 synonyms
    
    # Add language-specific context
    for lang, terms in language_context.items():
        if lang in query_lower:
            enhanced_terms.extend(terms[:3])  # Add top 3 terms
    
    # Create enhanced query
    if enhanced_terms:
        enhanced_query = f"Code example for {query}\n\nTechnical context: {', '.join(enhanced_terms)}\n\nSummary: Programming example demonstrating {query}"
    else:
        enhanced_query = f"Code example for {query}\n\nSummary: Programming example showing {query} implementation"
    
    return enhanced_query


def _is_meaningful_code_snippet(code_content: str) -> bool:
    """
    Determine if a code snippet is meaningful enough to include even if it's short.
    
    Args:
        code_content: The code content to evaluate
        
    Returns:
        True if the code snippet is meaningful, False otherwise
    """
    if not code_content or len(code_content.strip()) < 10:
        return False
    
    # Patterns that indicate meaningful code
    meaningful_patterns = [
        r'\bdef\s+\w+\s*\(',           # Python function definition
        r'\bclass\s+\w+',             # Class definition
        r'\bimport\s+\w+',            # Import statement
        r'\bfrom\s+\w+\s+import',     # From import statement
        r'\bfunction\s+\w+\s*\(',     # JavaScript function
        r'\bconst\s+\w+\s*=',         # JavaScript const
        r'\blet\s+\w+\s*=',           # JavaScript let
        r'\bvar\s+\w+\s*=',           # JavaScript var
        r'\bpublic\s+\w+',            # Public method/field
        r'\bprivate\s+\w+',           # Private method/field
        r'\bprotected\s+\w+',         # Protected method/field
        r'\bstatic\s+\w+',            # Static method/field
        r'\bSELECT\s+.*\bFROM\b',     # SQL SELECT statement
        r'\bINSERT\s+INTO\b',         # SQL INSERT statement
        r'\bUPDATE\s+.*\bSET\b',      # SQL UPDATE statement
        r'\bDELETE\s+FROM\b',         # SQL DELETE statement
        r'\bCREATE\s+TABLE\b',        # SQL CREATE TABLE
        r'\bfor\s+.*\s+in\s+',        # For loop
        r'\bwhile\s+.*:',             # While loop
        r'\bif\s+.*:',                # If statement
        r'\btry\s*:',                 # Try block
        r'\bexcept\s+.*:',            # Exception handling
        r'\basync\s+def\s+',          # Async function
        r'\bawait\s+\w+',             # Await statement
        r'\btype\s+\w+\s*=',          # TypeScript type definition
        r'\binterface\s+\w+',         # TypeScript interface
        r'\bstruct\s+\w+',            # Struct definition
        r'\bfn\s+\w+\s*\(',           # Rust function
        r'\bpub\s+fn\s+',             # Rust public function
        r'\bpkg\s+\w+',               # Go package
        r'\bfunc\s+\w+\s*\(',         # Go function
        r'\b@\w+',                    # Decorator/annotation
        r'\$\w+',                     # Shell variable
        r'\becho\s+',                 # Shell echo
        r'\bgrep\s+',                 # Shell grep
        r'\bawk\s+',                  # Shell awk
        r'\bsed\s+',                  # Shell sed
        r'\bcat\s+',                  # Shell cat
        r'\bls\s+',                   # Shell ls
        r'\bfind\s+',                 # Shell find
        r'\bDockerfile',              # Docker file
        r'\bFROM\s+\w+',              # Docker FROM
        r'\bRUN\s+',                  # Docker RUN
        r'\bCOPY\s+',                 # Docker COPY
        r'\bEXPOSE\s+',               # Docker EXPOSE
    ]
    
    # Check if any meaningful pattern is present
    for pattern in meaningful_patterns:
        if re.search(pattern, code_content, re.IGNORECASE):
            return True
    
    # Check for multi-line code (likely more meaningful)
    if '\n' in code_content.strip() and len(code_content.strip().split('\n')) >= 3:
        return True
    
    # Check for common programming constructs
    programming_keywords = [
        'function', 'class', 'import', 'export', 'const', 'let', 'var',
        'def', 'return', 'yield', 'async', 'await', 'lambda',
        'if', 'else', 'elif', 'for', 'while', 'try', 'except', 'finally',
        'public', 'private', 'protected', 'static', 'abstract',
        'interface', 'type', 'struct', 'enum', 'union',
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP',
        'npm', 'pip', 'cargo', 'go', 'mvn', 'gradle'
    ]
    
    word_count = 0
    for keyword in programming_keywords:
        if keyword.lower() in code_content.lower():
            word_count += 1
    
    # If it contains multiple programming keywords, it's likely meaningful
    return word_count >= 2


# Helper functions for optimized implementations
async def _process_contextual_embeddings(
    contents: List[str],
    urls: List[str],
    url_to_full_document: Dict[str, str],
    metadatas: List[Dict[str, Any]]
) -> List[str]:
    """
    Process contextual embeddings in parallel for better performance.
    
    Args:
        contents: List of content chunks
        urls: List of URLs
        url_to_full_document: Dictionary mapping URLs to full documents
        metadatas: List of metadata dictionaries
        
    Returns:
        List of contextual contents
    """
    # Prepare arguments for parallel processing
    process_args = []
    for j, content in enumerate(contents):
        url = urls[j]
        full_document = url_to_full_document.get(url, "")
        process_args.append((url, content, full_document))
    
    # Process in parallel using ThreadPoolExecutor
    contextual_contents = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all tasks and collect results
        future_to_idx = {executor.submit(process_chunk_with_context, arg): idx 
                        for idx, arg in enumerate(process_args)}
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_idx):
            idx = future_to_idx[future]
            try:
                result, success = future.result()
                contextual_contents.append(result)
                if success:
                    metadatas[idx]["contextual_embedding"] = True
            except Exception as e:
                print(f"Error processing chunk {idx}: {e}")
                # Use original content as fallback
                contextual_contents.append(contents[idx])
    
    # Sort results back into original order if needed
    if len(contextual_contents) != len(contents):
        print(f"Warning: Expected {len(contents)} results but got {len(contextual_contents)}")
        # Use original contents as fallback
        contextual_contents = contents
    
    return contextual_contents


async def initialize_performance_optimizations() -> None:
    """
    Initialize performance optimizations if available.
    """
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
        try:
            await initialize_query_optimizer()
            print("✓ Performance optimizations initialized successfully")
        except Exception as e:
            print(f"Warning: Failed to initialize performance optimizations: {e}")
    else:
        print("Performance optimizations not available")


def get_performance_stats() -> Dict[str, Any]:
    """
    Get comprehensive performance statistics.
    
    Returns:
        Dictionary containing performance metrics
    """
    stats = {
        "optimizations_available": PERFORMANCE_OPTIMIZATIONS_AVAILABLE,
        "memory_info": memory_monitor.get_memory_info(),
        "job_queue_stats": {
            "active_jobs": len(job_queue.active_jobs),
            "max_concurrent": job_queue.max_concurrent_jobs
        }
    }
    
    if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
        try:
            # Get cache stats
            if embedding_cache:
                stats["embedding_cache"] = embedding_cache.get_stats()
            if crawl_cache:
                stats["crawl_cache"] = crawl_cache.get_stats()
            if query_cache:
                stats["query_cache"] = query_cache.get_stats()
            
            # Get query optimizer stats
            optimizer = get_query_optimizer()
            stats["query_optimizer"] = optimizer.get_performance_stats()
            
        except Exception as e:
            stats["optimization_error"] = str(e)
    
    return stats