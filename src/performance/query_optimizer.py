"""
Database query optimization module for enhanced Supabase performance.

This module provides optimized query patterns, connection pooling, and batch operations
to significantly improve database performance and reduce latency.
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
import weakref
from supabase import Client, create_client
import json
from .cache_manager import query_cache


@dataclass
class QueryResult:
    """Container for query results with metadata."""
    data: Any
    execution_time: float
    cache_hit: bool = False
    rows_affected: int = 0


class QueryOptimizer:
    """
    Advanced query optimizer with connection pooling, batching, and caching.
    
    Features:
    - Async connection pooling with health checks
    - Intelligent query batching and bulk operations
    - Query result caching with invalidation
    - Performance monitoring and optimization hints
    - Prepared statement-like functionality
    """
    
    def __init__(self, supabase_url: str, supabase_key: str, max_connections: int = 10):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.max_connections = max_connections
        
        # Connection pool
        self._pool: List[Client] = []
        self._pool_lock = asyncio.Lock()
        self._active_connections: weakref.WeakSet = weakref.WeakSet()
        
        # Query performance tracking
        self._query_stats: Dict[str, Dict] = {}
        self._stats_lock = threading.Lock()
        
        # Batch operation queues
        self._batch_queues: Dict[str, List] = {}
        self._batch_timers: Dict[str, asyncio.Task] = {}
        self._batch_lock = asyncio.Lock()
        
        # Thread pool for CPU-intensive operations
        self._thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Pre-compiled query patterns
        self._prepared_queries = {
            'search_documents': self._build_search_query,
            'search_code_examples': self._build_code_search_query,
            'batch_insert_pages': self._build_batch_insert_query,
            'batch_insert_code': self._build_batch_code_insert_query,
            'update_source_summary': self._build_source_update_query,
            'get_sources': self._build_sources_query
        }

    async def initialize_pool(self) -> None:
        """Initialize the connection pool with health checks."""
        async with self._pool_lock:
            for _ in range(self.max_connections):
                try:
                    client = create_client(self.supabase_url, self.supabase_key)
                    # Test connection
                    await self._test_connection(client)
                    self._pool.append(client)
                except Exception as e:
                    print(f"Failed to create connection: {e}")
                    break

    async def _test_connection(self, client: Client) -> bool:
        """Test if a connection is healthy."""
        try:
            # Simple health check query
            result = client.table('sources').select('count').limit(1).execute()
            return True
        except Exception:
            return False

    async def get_connection(self) -> Client:
        """Get a connection from the pool."""
        async with self._pool_lock:
            # Try to get a connection from pool
            while self._pool:
                client = self._pool.pop()
                if await self._test_connection(client):
                    self._active_connections.add(client)
                    return client
            
            # Pool is empty or unhealthy, create new connection
            try:
                client = create_client(self.supabase_url, self.supabase_key)
                await self._test_connection(client)
                self._active_connections.add(client)
                return client
            except Exception as e:
                raise RuntimeError(f"Failed to create database connection: {e}")

    async def return_connection(self, client: Client) -> None:
        """Return a connection to the pool."""
        async with self._pool_lock:
            if len(self._pool) < self.max_connections:
                if await self._test_connection(client):
                    self._pool.append(client)
                    if client in self._active_connections:
                        self._active_connections.discard(client)

    async def execute_optimized_query(
        self,
        query_name: str,
        params: Dict[str, Any],
        cache_ttl: int = 300,
        use_cache: bool = True
    ) -> QueryResult:
        """
        Execute an optimized query with caching and performance monitoring.
        
        Args:
            query_name: Name of the prepared query
            params: Query parameters
            cache_ttl: Cache time-to-live in seconds
            use_cache: Whether to use caching
            
        Returns:
            QueryResult with data and performance metrics
        """
        start_time = time.time()
        
        # Generate cache key
        cache_key = f"{query_name}:{hash(json.dumps(params, sort_keys=True))}"
        
        # Check cache first
        if use_cache:
            cached_result = await query_cache.get(cache_key)
            if cached_result is not None:
                execution_time = time.time() - start_time
                self._update_query_stats(query_name, execution_time, True)
                return QueryResult(
                    data=cached_result,
                    execution_time=execution_time,
                    cache_hit=True
                )
        
        # Execute query
        if query_name not in self._prepared_queries:
            raise ValueError(f"Unknown query: {query_name}")
        
        client = await self.get_connection()
        try:
            query_builder = self._prepared_queries[query_name]
            result = await query_builder(client, params)
            
            execution_time = time.time() - start_time
            self._update_query_stats(query_name, execution_time, False)
            
            # Cache result
            if use_cache:
                await query_cache.set(cache_key, result, ttl=cache_ttl)
            
            return QueryResult(
                data=result,
                execution_time=execution_time,
                cache_hit=False,
                rows_affected=len(result) if isinstance(result, list) else 1
            )
            
        finally:
            await self.return_connection(client)

    def _update_query_stats(self, query_name: str, execution_time: float, cache_hit: bool) -> None:
        """Update query performance statistics."""
        with self._stats_lock:
            if query_name not in self._query_stats:
                self._query_stats[query_name] = {
                    'total_executions': 0,
                    'total_time': 0,
                    'cache_hits': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'avg_time': 0
                }
            
            stats = self._query_stats[query_name]
            stats['total_executions'] += 1
            
            if cache_hit:
                stats['cache_hits'] += 1
            else:
                stats['total_time'] += execution_time
                stats['min_time'] = min(stats['min_time'], execution_time)
                stats['max_time'] = max(stats['max_time'], execution_time)
                stats['avg_time'] = stats['total_time'] / (stats['total_executions'] - stats['cache_hits'])

    # Optimized query builders
    async def _build_search_query(self, client: Client, params: Dict[str, Any]) -> List[Dict]:
        """Build optimized document search query."""
        query_embedding = params['query_embedding']
        match_count = params.get('match_count', 10)
        filter_metadata = params.get('filter_metadata')
        
        # Use RPC function for vector similarity search
        rpc_params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        if filter_metadata:
            rpc_params['filter'] = filter_metadata
            
        result = client.rpc('match_crawled_pages', rpc_params).execute()
        return result.data

    async def _build_code_search_query(self, client: Client, params: Dict[str, Any]) -> List[Dict]:
        """Build optimized code example search query."""
        query_embedding = params['query_embedding']
        match_count = params.get('match_count', 10)
        filter_metadata = params.get('filter_metadata')
        source_id = params.get('source_id')
        
        rpc_params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        if filter_metadata:
            rpc_params['filter'] = filter_metadata
        if source_id:
            rpc_params['source_filter'] = source_id
            
        result = client.rpc('match_code_examples', rpc_params).execute()
        return result.data

    async def _build_batch_insert_query(self, client: Client, params: Dict[str, Any]) -> List[Dict]:
        """Build optimized batch insert query for crawled pages."""
        batch_data = params['batch_data']
        table_name = params.get('table_name', 'crawled_pages')
        
        # Delete existing records first
        if 'urls_to_delete' in params:
            urls = params['urls_to_delete']
            if urls:
                client.table(table_name).delete().in_('url', urls).execute()
        
        # Batch insert with optimal chunk size
        chunk_size = 50  # Optimal for Supabase
        results = []
        
        for i in range(0, len(batch_data), chunk_size):
            chunk = batch_data[i:i + chunk_size]
            result = client.table(table_name).insert(chunk).execute()
            results.extend(result.data)
            
        return results

    async def _build_batch_code_insert_query(self, client: Client, params: Dict[str, Any]) -> List[Dict]:
        """Build optimized batch insert query for code examples."""
        return await self._build_batch_insert_query(client, {
            **params,
            'table_name': 'code_examples'
        })

    async def _build_source_update_query(self, client: Client, params: Dict[str, Any]) -> Dict:
        """Build optimized source update/insert query."""
        source_id = params['source_id']
        summary = params['summary']
        word_count = params['word_count']
        
        # Try upsert pattern for better performance
        result = client.table('sources').upsert({
            'source_id': source_id,
            'summary': summary,
            'total_word_count': word_count,
            'updated_at': 'now()'
        }).execute()
        
        return result.data

    async def _build_sources_query(self, client: Client, params: Dict[str, Any]) -> List[Dict]:
        """Build optimized sources query."""
        limit = params.get('limit', 50)
        offset = params.get('offset', 0)
        
        result = client.table('sources').select('*').range(offset, offset + limit - 1).execute()
        return result.data

    # Batch operation management
    async def queue_batch_operation(
        self,
        operation_type: str,
        data: Dict[str, Any],
        batch_size: int = 50,
        max_wait_time: float = 2.0
    ) -> None:
        """Queue data for batch processing."""
        async with self._batch_lock:
            if operation_type not in self._batch_queues:
                self._batch_queues[operation_type] = []
            
            self._batch_queues[operation_type].append(data)
            
            # Check if batch is ready
            if len(self._batch_queues[operation_type]) >= batch_size:
                await self._process_batch(operation_type)
            else:
                # Set timer for max wait time
                if operation_type not in self._batch_timers:
                    self._batch_timers[operation_type] = asyncio.create_task(
                        self._batch_timer(operation_type, max_wait_time)
                    )

    async def _batch_timer(self, operation_type: str, max_wait_time: float) -> None:
        """Timer for batch processing."""
        await asyncio.sleep(max_wait_time)
        async with self._batch_lock:
            if operation_type in self._batch_queues and self._batch_queues[operation_type]:
                await self._process_batch(operation_type)

    async def _process_batch(self, operation_type: str) -> None:
        """Process a batch of operations."""
        if operation_type not in self._batch_queues:
            return
            
        batch_data = self._batch_queues[operation_type]
        self._batch_queues[operation_type] = []
        
        # Cancel timer
        if operation_type in self._batch_timers:
            self._batch_timers[operation_type].cancel()
            del self._batch_timers[operation_type]
        
        if not batch_data:
            return
            
        # Process batch based on operation type
        client = await self.get_connection()
        try:
            if operation_type == 'insert_pages':
                await self._process_page_batch(client, batch_data)
            elif operation_type == 'insert_code':
                await self._process_code_batch(client, batch_data)
            elif operation_type == 'update_sources':
                await self._process_source_batch(client, batch_data)
        finally:
            await self.return_connection(client)

    async def _process_page_batch(self, client: Client, batch_data: List[Dict]) -> None:
        """Process a batch of page insertions."""
        try:
            # Combine all batch data
            all_data = []
            urls_to_delete = set()
            
            for item in batch_data:
                all_data.extend(item.get('data', []))
                urls_to_delete.update(item.get('urls_to_delete', []))
            
            # Delete existing records
            if urls_to_delete:
                client.table('crawled_pages').delete().in_('url', list(urls_to_delete)).execute()
            
            # Batch insert
            chunk_size = 50
            for i in range(0, len(all_data), chunk_size):
                chunk = all_data[i:i + chunk_size]
                client.table('crawled_pages').insert(chunk).execute()
                
        except Exception as e:
            print(f"Error processing page batch: {e}")

    async def _process_code_batch(self, client: Client, batch_data: List[Dict]) -> None:
        """Process a batch of code example insertions."""
        try:
            all_data = []
            urls_to_delete = set()
            
            for item in batch_data:
                all_data.extend(item.get('data', []))
                urls_to_delete.update(item.get('urls_to_delete', []))
            
            # Delete existing records
            if urls_to_delete:
                client.table('code_examples').delete().in_('url', list(urls_to_delete)).execute()
            
            # Batch insert
            chunk_size = 50
            for i in range(0, len(all_data), chunk_size):
                chunk = all_data[i:i + chunk_size]
                client.table('code_examples').insert(chunk).execute()
                
        except Exception as e:
            print(f"Error processing code batch: {e}")

    async def _process_source_batch(self, client: Client, batch_data: List[Dict]) -> None:
        """Process a batch of source updates."""
        try:
            # Use upsert for batch source updates
            upsert_data = []
            for item in batch_data:
                upsert_data.append({
                    'source_id': item['source_id'],
                    'summary': item['summary'],
                    'total_word_count': item['word_count'],
                    'updated_at': 'now()'
                })
            
            client.table('sources').upsert(upsert_data).execute()
            
        except Exception as e:
            print(f"Error processing source batch: {e}")

    # Performance monitoring
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        with self._stats_lock:
            stats = {}
            for query_name, query_stats in self._query_stats.items():
                cache_hit_rate = (
                    query_stats['cache_hits'] / query_stats['total_executions']
                    if query_stats['total_executions'] > 0 else 0
                )
                
                stats[query_name] = {
                    'total_executions': query_stats['total_executions'],
                    'cache_hit_rate': cache_hit_rate,
                    'avg_execution_time': query_stats['avg_time'],
                    'min_execution_time': query_stats['min_time'],
                    'max_execution_time': query_stats['max_time']
                }
            
            return {
                'query_stats': stats,
                'active_connections': len(self._active_connections),
                'pool_size': len(self._pool),
                'batch_queue_sizes': {
                    op: len(queue) for op, queue in self._batch_queues.items()
                }
            }

    async def cleanup(self) -> None:
        """Clean up resources."""
        # Cancel batch timers
        for timer in self._batch_timers.values():
            timer.cancel()
        
        # Close thread pool
        self._thread_pool.shutdown(wait=False)
        
        # Clear pools
        async with self._pool_lock:
            self._pool.clear()
            self._active_connections.clear()


# Global query optimizer instance
query_optimizer: Optional[QueryOptimizer] = None


def get_query_optimizer() -> QueryOptimizer:
    """Get the global query optimizer instance."""
    global query_optimizer
    if query_optimizer is None:
        import os
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        query_optimizer = QueryOptimizer(supabase_url, supabase_key)
    
    return query_optimizer


async def initialize_query_optimizer() -> None:
    """Initialize the global query optimizer."""
    optimizer = get_query_optimizer()
    await optimizer.initialize_pool()