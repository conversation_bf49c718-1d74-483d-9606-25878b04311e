"""
Performance monitoring and metrics collection for MCP Crawl4AI RAG server.

This module provides comprehensive performance monitoring, profiling, and 
optimization recommendations to maintain peak system performance.
"""
import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
import json
import logging
import sys
from functools import wraps


@dataclass
class PerformanceMetric:
    """Represents a single performance metric measurement."""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class OperationProfile:
    """Represents profiling data for a specific operation."""
    operation_name: str
    total_calls: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    p95_time: float = 0.0
    p99_time: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    error_count: int = 0
    last_error: Optional[str] = None


class PerformanceMonitor:
    """
    Comprehensive performance monitoring system with real-time metrics,
    profiling, and optimization recommendations.
    
    Features:
    - Real-time performance metrics collection
    - Operation profiling with percentiles
    - Memory usage monitoring and alerts
    - Database query performance tracking
    - Cache hit rate monitoring
    - System resource utilization
    - Performance bottleneck detection
    - Optimization recommendations
    """
    
    def __init__(self):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.operation_profiles: Dict[str, OperationProfile] = {}
        self.alerts: List[Dict[str, Any]] = []
        self.lock = threading.Lock()
        
        # System monitoring
        self.process = psutil.Process()
        self.monitoring_task: Optional[asyncio.Task] = None
        self.monitoring_interval = 5.0  # seconds
        
        # Performance thresholds
        self.thresholds = {
            'memory_usage_percent': 85.0,
            'cpu_usage_percent': 80.0,
            'response_time_ms': 2000.0,
            'database_query_time_ms': 500.0,
            'cache_hit_rate_percent': 70.0,
            'error_rate_percent': 5.0
        }
        
        # Optimization recommendations
        self.recommendations: List[str] = []
        
        # Logger setup
        self.logger = logging.getLogger(__name__)
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def start_monitoring(self) -> None:
        """Start the background monitoring task."""
        if self.monitoring_task is None or self.monitoring_task.done():
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop that collects system metrics."""
        while True:
            try:
                await self._collect_system_metrics()
                await self._analyze_performance()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")

    async def _collect_system_metrics(self) -> None:
        """Collect system-level performance metrics."""
        try:
            # Memory metrics
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            self.record_metric("memory_rss_mb", memory_info.rss / (1024 * 1024))
            self.record_metric("memory_vms_mb", memory_info.vms / (1024 * 1024))
            self.record_metric("memory_percent", memory_percent)
            
            # CPU metrics
            cpu_percent = self.process.cpu_percent()
            self.record_metric("cpu_percent", cpu_percent)
            
            # System memory
            sys_memory = psutil.virtual_memory()
            self.record_metric("system_memory_percent", sys_memory.percent)
            self.record_metric("system_memory_available_mb", sys_memory.available / (1024 * 1024))
            
            # Disk I/O
            io_counters = self.process.io_counters()
            self.record_metric("disk_read_bytes", io_counters.read_bytes)
            self.record_metric("disk_write_bytes", io_counters.write_bytes)
            
            # Network connections
            connections = self.process.connections()
            self.record_metric("network_connections", len(connections))
            
            # Thread count
            self.record_metric("thread_count", self.process.num_threads())
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")

    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a performance metric."""
        with self.lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                timestamp=time.time(),
                tags=tags or {}
            )
            self.metrics[name].append(metric)

    def profile_operation(self, operation_name: str):
        """Decorator to profile function execution time."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                error = None
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    execution_time = time.time() - start_time
                    self._record_operation_profile(operation_name, execution_time, error)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                error = None
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    execution_time = time.time() - start_time
                    self._record_operation_profile(operation_name, execution_time, error)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator

    def _record_operation_profile(self, operation_name: str, execution_time: float, error: Optional[str] = None) -> None:
        """Record operation profiling data."""
        with self.lock:
            if operation_name not in self.operation_profiles:
                self.operation_profiles[operation_name] = OperationProfile(operation_name)
            
            profile = self.operation_profiles[operation_name]
            profile.total_calls += 1
            profile.total_time += execution_time
            profile.min_time = min(profile.min_time, execution_time)
            profile.max_time = max(profile.max_time, execution_time)
            profile.avg_time = profile.total_time / profile.total_calls
            profile.recent_times.append(execution_time)
            
            if error:
                profile.error_count += 1
                profile.last_error = error
            
            # Calculate percentiles
            if profile.recent_times:
                sorted_times = sorted(profile.recent_times)
                profile.p95_time = sorted_times[int(len(sorted_times) * 0.95)]
                profile.p99_time = sorted_times[int(len(sorted_times) * 0.99)]

    async def _analyze_performance(self) -> None:
        """Analyze current performance and generate recommendations."""
        try:
            # Check memory usage
            if self.metrics['memory_percent']:
                current_memory = self.metrics['memory_percent'][-1].value
                if current_memory > self.thresholds['memory_usage_percent']:
                    self._add_alert(
                        "high_memory_usage",
                        f"Memory usage is {current_memory:.1f}% (threshold: {self.thresholds['memory_usage_percent']}%)",
                        "warning"
                    )
                    self._add_recommendation("Consider increasing memory limits or enabling more aggressive garbage collection")
            
            # Check CPU usage
            if self.metrics['cpu_percent']:
                recent_cpu = [m.value for m in list(self.metrics['cpu_percent'])[-10:]]
                if recent_cpu:
                    avg_cpu = statistics.mean(recent_cpu)
                    if avg_cpu > self.thresholds['cpu_usage_percent']:
                        self._add_alert(
                            "high_cpu_usage",
                            f"Average CPU usage is {avg_cpu:.1f}% (threshold: {self.thresholds['cpu_usage_percent']}%)",
                            "warning"
                        )
                        self._add_recommendation("Consider optimizing CPU-intensive operations or increasing CPU allocation")
            
            # Analyze operation profiles
            for operation_name, profile in self.operation_profiles.items():
                if profile.avg_time > self.thresholds['response_time_ms'] / 1000:
                    self._add_alert(
                        "slow_operation",
                        f"Operation '{operation_name}' average time is {profile.avg_time*1000:.1f}ms",
                        "warning"
                    )
                    self._add_recommendation(f"Consider optimizing '{operation_name}' operation")
                
                if profile.total_calls > 0:
                    error_rate = (profile.error_count / profile.total_calls) * 100
                    if error_rate > self.thresholds['error_rate_percent']:
                        self._add_alert(
                            "high_error_rate",
                            f"Operation '{operation_name}' error rate is {error_rate:.1f}%",
                            "error"
                        )
                        self._add_recommendation(f"Investigate errors in '{operation_name}' operation")
            
        except Exception as e:
            self.logger.error(f"Error analyzing performance: {e}")

    def _add_alert(self, alert_type: str, message: str, severity: str) -> None:
        """Add a performance alert."""
        alert = {
            "type": alert_type,
            "message": message,
            "severity": severity,
            "timestamp": time.time()
        }
        self.alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-50:]
        
        # Log alert
        if severity == "error":
            self.logger.error(f"ALERT: {message}")
        elif severity == "warning":
            self.logger.warning(f"ALERT: {message}")
        else:
            self.logger.info(f"ALERT: {message}")

    def _add_recommendation(self, recommendation: str) -> None:
        """Add a performance optimization recommendation."""
        if recommendation not in self.recommendations:
            self.recommendations.append(recommendation)
            
            # Keep only recent recommendations
            if len(self.recommendations) > 20:
                self.recommendations = self.recommendations[-10:]

    def get_performance_report(self) -> Dict[str, Any]:
        """Generate a comprehensive performance report."""
        with self.lock:
            report = {
                "timestamp": time.time(),
                "system_metrics": self._get_system_metrics_summary(),
                "operation_profiles": self._get_operation_profiles_summary(),
                "alerts": self.alerts[-10:],  # Recent alerts
                "recommendations": self.recommendations,
                "performance_score": self._calculate_performance_score()
            }
            
            return report

    def _get_system_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of system metrics."""
        summary = {}
        
        for metric_name, metric_values in self.metrics.items():
            if metric_values:
                recent_values = [m.value for m in list(metric_values)[-20:]]
                summary[metric_name] = {
                    "current": recent_values[-1],
                    "average": statistics.mean(recent_values),
                    "min": min(recent_values),
                    "max": max(recent_values),
                    "trend": self._calculate_trend(recent_values)
                }
        
        return summary

    def _get_operation_profiles_summary(self) -> Dict[str, Any]:
        """Get summary of operation profiles."""
        profiles = {}
        
        for operation_name, profile in self.operation_profiles.items():
            profiles[operation_name] = {
                "total_calls": profile.total_calls,
                "avg_time_ms": profile.avg_time * 1000,
                "p95_time_ms": profile.p95_time * 1000,
                "p99_time_ms": profile.p99_time * 1000,
                "error_count": profile.error_count,
                "error_rate": (profile.error_count / profile.total_calls * 100) if profile.total_calls > 0 else 0,
                "last_error": profile.last_error
            }
        
        return profiles

    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for a series of values."""
        if len(values) < 2:
            return "stable"
        
        # Simple linear trend calculation
        n = len(values)
        x = list(range(n))
        y = values
        
        # Calculate slope
        x_mean = statistics.mean(x)
        y_mean = statistics.mean(y)
        
        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return "stable"
        
        slope = numerator / denominator
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"

    def _calculate_performance_score(self) -> float:
        """Calculate overall performance score (0-100)."""
        score = 100.0
        
        # Memory usage penalty
        if self.metrics['memory_percent']:
            memory_usage = self.metrics['memory_percent'][-1].value
            if memory_usage > 80:
                score -= (memory_usage - 80) * 2
        
        # CPU usage penalty
        if self.metrics['cpu_percent']:
            cpu_usage = self.metrics['cpu_percent'][-1].value
            if cpu_usage > 70:
                score -= (cpu_usage - 70) * 1.5
        
        # Operation performance penalty
        for profile in self.operation_profiles.values():
            if profile.avg_time > 1.0:  # 1 second threshold
                score -= min(10, profile.avg_time * 2)
            
            if profile.total_calls > 0:
                error_rate = (profile.error_count / profile.total_calls) * 100
                if error_rate > 1:
                    score -= error_rate * 5
        
        # Alert penalty
        recent_alerts = [a for a in self.alerts if time.time() - a['timestamp'] < 300]  # Last 5 minutes
        score -= len(recent_alerts) * 2
        
        return max(0, min(100, score))

    def get_bottleneck_analysis(self) -> Dict[str, Any]:
        """Analyze performance bottlenecks and suggest optimizations."""
        bottlenecks = []
        
        # Memory bottleneck
        if self.metrics['memory_percent']:
            current_memory = self.metrics['memory_percent'][-1].value
            if current_memory > 80:
                bottlenecks.append({
                    "type": "memory",
                    "severity": "high" if current_memory > 90 else "medium",
                    "current_value": current_memory,
                    "threshold": 80,
                    "recommendation": "Enable more aggressive garbage collection or increase memory allocation"
                })
        
        # CPU bottleneck
        if self.metrics['cpu_percent']:
            recent_cpu = [m.value for m in list(self.metrics['cpu_percent'])[-10:]]
            if recent_cpu:
                avg_cpu = statistics.mean(recent_cpu)
                if avg_cpu > 70:
                    bottlenecks.append({
                        "type": "cpu",
                        "severity": "high" if avg_cpu > 85 else "medium",
                        "current_value": avg_cpu,
                        "threshold": 70,
                        "recommendation": "Optimize CPU-intensive operations or increase CPU allocation"
                    })
        
        # Operation bottlenecks
        for operation_name, profile in self.operation_profiles.items():
            if profile.p95_time > 1.0:  # 1 second P95 threshold
                bottlenecks.append({
                    "type": "operation",
                    "operation": operation_name,
                    "severity": "high" if profile.p95_time > 2.0 else "medium",
                    "current_value": profile.p95_time * 1000,
                    "threshold": 1000,
                    "recommendation": f"Optimize '{operation_name}' operation or add caching"
                })
        
        return {
            "bottlenecks": bottlenecks,
            "analysis_timestamp": time.time(),
            "total_bottlenecks": len(bottlenecks)
        }

    def stop_monitoring(self) -> None:
        """Stop the monitoring task."""
        if self.monitoring_task:
            self.monitoring_task.cancel()

    def reset_metrics(self) -> None:
        """Reset all metrics and profiles."""
        with self.lock:
            self.metrics.clear()
            self.operation_profiles.clear()
            self.alerts.clear()
            self.recommendations.clear()


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    return performance_monitor


# Convenience decorators
def profile_async(operation_name: str):
    """Decorator to profile async functions."""
    return performance_monitor.profile_operation(operation_name)


def profile_sync(operation_name: str):
    """Decorator to profile sync functions."""
    return performance_monitor.profile_operation(operation_name)


# Context manager for profiling code blocks
class ProfileContext:
    """Context manager for profiling code blocks."""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            execution_time = time.time() - self.start_time
            error = str(exc_val) if exc_val else None
            performance_monitor._record_operation_profile(
                self.operation_name, 
                execution_time, 
                error
            )


def profile_context(operation_name: str) -> ProfileContext:
    """Create a profiling context manager."""
    return ProfileContext(operation_name)