"""
Performance optimization package for MCP Crawl4AI RAG server.

This package provides comprehensive performance optimizations including:
- Intelligent caching system
- Database query optimization
- Performance monitoring and metrics
- Resource management and profiling
"""

from .cache_manager import (
    PerformanceCache,
    EmbeddingCache,
    CrawlResultCache,
    embedding_cache,
    crawl_cache,
    query_cache
)

from .query_optimizer import (
    QueryOptimizer,
    get_query_optimizer,
    initialize_query_optimizer
)

from .monitor import (
    PerformanceMonitor,
    performance_monitor,
    get_performance_monitor,
    profile_async,
    profile_sync,
    profile_context
)

__all__ = [
    # Cache management
    'PerformanceCache',
    'EmbeddingCache', 
    'CrawlResultCache',
    'embedding_cache',
    'crawl_cache',
    'query_cache',
    
    # Query optimization
    'QueryOptimizer',
    'get_query_optimizer',
    'initialize_query_optimizer',
    
    # Performance monitoring
    'PerformanceMonitor',
    'performance_monitor',
    'get_performance_monitor',
    'profile_async',
    'profile_sync',
    'profile_context'
]