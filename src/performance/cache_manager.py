"""
High-performance caching system for MCP Crawl4AI RAG server.

This module provides intelligent caching for embeddings, crawl results, and database queries
to significantly improve response times and reduce API costs.
"""
import asyncio
import hashlib
import json
import os
import pickle
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from threading import Lock
import weakref
import gc


@dataclass
class CacheEntry:
    """Represents a cache entry with metadata."""
    data: Any
    timestamp: float
    hit_count: int = 0
    last_access: float = 0
    size_bytes: int = 0
    ttl: float = 3600  # Default 1 hour TTL


class PerformanceCache:
    """
    High-performance multi-level cache with LRU eviction and intelligent preloading.
    
    Features:
    - Memory cache with size limits and TTL
    - Persistent disk cache for expensive operations
    - LRU eviction with access patterns
    - Automatic cache warming
    - Memory pressure monitoring
    """
    
    def __init__(
        self,
        max_memory_size: int = 500_000_000,  # 500MB default
        max_disk_size: int = 2_000_000_000,  # 2GB default
        cache_dir: str = ".cache"
    ):
        self.max_memory_size = max_memory_size
        self.max_disk_size = max_disk_size
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Memory cache
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._memory_size = 0
        self._lock = asyncio.Lock()
        self._sync_lock = Lock()
        
        # Access pattern tracking
        self._access_log: List[Tuple[str, float]] = []
        self._preload_candidates: weakref.WeakSet = weakref.WeakSet()
        
        # Performance metrics
        self.metrics = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "disk_hits": 0,
            "disk_misses": 0,
            "preloads": 0
        }
        
        # Cache warming task (delayed initialization)
        self._warming_task: Optional[asyncio.Task] = None
        self._warming_enabled = True

    def _generate_cache_key(self, key_data: Union[str, Dict, List]) -> str:
        """Generate a consistent cache key from various data types."""
        if isinstance(key_data, str):
            content = key_data
        else:
            content = json.dumps(key_data, sort_keys=True)
        
        return hashlib.sha256(content.encode()).hexdigest()[:16]

    def _estimate_size(self, data: Any) -> int:
        """Estimate the memory size of data."""
        try:
            if hasattr(data, '__sizeof__'):
                return data.__sizeof__()
            elif isinstance(data, (list, tuple)):
                return sum(self._estimate_size(item) for item in data)
            elif isinstance(data, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) 
                          for k, v in data.items())
            elif isinstance(data, str):
                return len(data.encode('utf-8'))
            else:
                # Fallback: use pickle size estimate
                return len(pickle.dumps(data))
        except Exception:
            return 1024  # Default 1KB estimate

    async def get(self, key: str, default: Any = None) -> Any:
        """Get item from cache with intelligent fallback to disk."""
        cache_key = self._generate_cache_key(key)
        
        # Ensure cache warming is started if event loop is available
        if self._warming_enabled and (self._warming_task is None or self._warming_task.done()):
            self._start_cache_warming()
        
        # Check memory cache first
        async with self._lock:
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                
                # Check TTL
                if time.time() - entry.timestamp <= entry.ttl:
                    entry.hit_count += 1
                    entry.last_access = time.time()
                    self.metrics["hits"] += 1
                    self._log_access(cache_key)
                    return entry.data
                else:
                    # Expired, remove from memory
                    del self._memory_cache[cache_key]
                    self._memory_size -= entry.size_bytes

        # Check disk cache
        disk_file = self.cache_dir / f"{cache_key}.cache"
        if disk_file.exists():
            try:
                with open(disk_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                # Check disk cache TTL
                if time.time() - cache_data['timestamp'] <= cache_data.get('ttl', 3600):
                    self.metrics["disk_hits"] += 1
                    
                    # Promote to memory cache if frequently accessed
                    data = cache_data['data']
                    await self._set_memory(cache_key, data, cache_data.get('ttl', 3600))
                    
                    return data
                else:
                    # Expired disk cache
                    disk_file.unlink()
                    self.metrics["disk_misses"] += 1
            except Exception as e:
                print(f"Error reading disk cache {cache_key}: {e}")
                disk_file.unlink(missing_ok=True)

        self.metrics["misses"] += 1
        return default

    async def set(
        self, 
        key: str, 
        data: Any, 
        ttl: float = 3600,
        persist_to_disk: bool = True
    ) -> None:
        """Set item in cache with optional disk persistence."""
        cache_key = self._generate_cache_key(key)
        
        # Set in memory cache
        await self._set_memory(cache_key, data, ttl)
        
        # Optionally persist to disk for expensive operations
        if persist_to_disk:
            await self._set_disk(cache_key, data, ttl)

    async def _set_memory(self, cache_key: str, data: Any, ttl: float) -> None:
        """Set item in memory cache with LRU eviction."""
        async with self._lock:
            size_bytes = self._estimate_size(data)
            
            # Check if we need to evict items
            while (self._memory_size + size_bytes > self.max_memory_size and 
                   self._memory_cache):
                await self._evict_lru()
            
            # Create cache entry
            entry = CacheEntry(
                data=data,
                timestamp=time.time(),
                size_bytes=size_bytes,
                ttl=ttl,
                last_access=time.time()
            )
            
            # Remove existing entry if present
            if cache_key in self._memory_cache:
                self._memory_size -= self._memory_cache[cache_key].size_bytes
            
            self._memory_cache[cache_key] = entry
            self._memory_size += size_bytes

    async def _set_disk(self, cache_key: str, data: Any, ttl: float) -> None:
        """Set item in disk cache asynchronously."""
        try:
            disk_file = self.cache_dir / f"{cache_key}.cache"
            cache_data = {
                'data': data,
                'timestamp': time.time(),
                'ttl': ttl
            }
            
            # Write to temporary file first, then rename for atomicity
            temp_file = disk_file.with_suffix('.tmp')
            with open(temp_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            temp_file.rename(disk_file)
            
            # Check disk cache size and clean if needed
            await self._cleanup_disk_cache()
            
        except Exception as e:
            print(f"Error writing disk cache {cache_key}: {e}")

    async def _evict_lru(self) -> None:
        """Evict least recently used item from memory cache."""
        if not self._memory_cache:
            return
            
        # Find LRU item
        lru_key = min(
            self._memory_cache.keys(),
            key=lambda k: self._memory_cache[k].last_access
        )
        
        entry = self._memory_cache[lru_key]
        self._memory_size -= entry.size_bytes
        del self._memory_cache[lru_key]
        self.metrics["evictions"] += 1

    async def _cleanup_disk_cache(self) -> None:
        """Clean up disk cache if it exceeds size limits."""
        try:
            cache_files = list(self.cache_dir.glob("*.cache"))
            if not cache_files:
                return
                
            # Calculate total size
            total_size = sum(f.stat().st_size for f in cache_files)
            
            if total_size > self.max_disk_size:
                # Sort by access time and remove oldest
                cache_files.sort(key=lambda f: f.stat().st_mtime)
                
                while total_size > self.max_disk_size * 0.8 and cache_files:
                    file_to_remove = cache_files.pop(0)
                    file_size = file_to_remove.stat().st_size
                    file_to_remove.unlink()
                    total_size -= file_size
                    
        except Exception as e:
            print(f"Error cleaning disk cache: {e}")

    def _log_access(self, cache_key: str) -> None:
        """Log cache access for pattern analysis."""
        with self._sync_lock:
            self._access_log.append((cache_key, time.time()))
            
            # Keep only recent access log (last 1000 entries)
            if len(self._access_log) > 1000:
                self._access_log = self._access_log[-500:]

    def _start_cache_warming(self) -> None:
        """Start background cache warming task."""
        if not self._warming_enabled:
            return
            
        try:
            loop = asyncio.get_running_loop()
            if self._warming_task is None or self._warming_task.done():
                self._warming_task = asyncio.create_task(self._cache_warming_loop())
        except RuntimeError:
            # No running event loop, skip warming initialization
            pass

    async def _cache_warming_loop(self) -> None:
        """Background task to warm cache based on access patterns."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._warm_frequently_accessed()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Cache warming error: {e}")

    async def _warm_frequently_accessed(self) -> None:
        """Identify and preload frequently accessed cache keys."""
        with self._sync_lock:
            if len(self._access_log) < 10:
                return
                
            # Count frequency of access
            frequency = {}
            recent_threshold = time.time() - 3600  # Last hour
            
            for key, access_time in self._access_log:
                if access_time > recent_threshold:
                    frequency[key] = frequency.get(key, 0) + 1
            
            # Find top candidates for preloading
            candidates = sorted(
                frequency.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]
            
            for cache_key, freq in candidates:
                if freq >= 3 and cache_key not in self._memory_cache:
                    # Try to load from disk to memory
                    disk_file = self.cache_dir / f"{cache_key}.cache"
                    if disk_file.exists():
                        try:
                            with open(disk_file, 'rb') as f:
                                cache_data = pickle.load(f)
                            
                            if (time.time() - cache_data['timestamp'] <= 
                                cache_data.get('ttl', 3600)):
                                await self._set_memory(
                                    cache_key, 
                                    cache_data['data'], 
                                    cache_data.get('ttl', 3600)
                                )
                                self.metrics["preloads"] += 1
                        except Exception as e:
                            print(f"Error preloading cache {cache_key}: {e}")

    async def invalidate(self, key: str) -> None:
        """Invalidate a specific cache key."""
        cache_key = self._generate_cache_key(key)
        
        async with self._lock:
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                self._memory_size -= entry.size_bytes
                del self._memory_cache[cache_key]
        
        # Remove from disk
        disk_file = self.cache_dir / f"{cache_key}.cache"
        disk_file.unlink(missing_ok=True)

    async def clear(self) -> None:
        """Clear all cache data."""
        async with self._lock:
            self._memory_cache.clear()
            self._memory_size = 0
        
        # Clear disk cache
        for cache_file in self.cache_dir.glob("*.cache"):
            cache_file.unlink()
        
        gc.collect()

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        hit_rate = (
            self.metrics["hits"] / (self.metrics["hits"] + self.metrics["misses"])
            if (self.metrics["hits"] + self.metrics["misses"]) > 0 else 0
        )
        
        disk_hit_rate = (
            self.metrics["disk_hits"] / 
            (self.metrics["disk_hits"] + self.metrics["disk_misses"])
            if (self.metrics["disk_hits"] + self.metrics["disk_misses"]) > 0 else 0
        )
        
        return {
            "memory_cache_size": len(self._memory_cache),
            "memory_usage_mb": self._memory_size / (1024 * 1024),
            "memory_limit_mb": self.max_memory_size / (1024 * 1024),
            "hit_rate": hit_rate,
            "disk_hit_rate": disk_hit_rate,
            "total_hits": self.metrics["hits"],
            "total_misses": self.metrics["misses"],
            "evictions": self.metrics["evictions"],
            "preloads": self.metrics["preloads"]
        }


# Specialized caches for different data types
class EmbeddingCache(PerformanceCache):
    """Specialized cache for embeddings with content-based invalidation."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.content_hashes: Dict[str, str] = {}

    async def get_embedding(
        self, 
        text: str, 
        model: str = "text-embedding-3-small"
    ) -> Optional[List[float]]:
        """Get embedding from cache, checking content hash for validity."""
        content_hash = hashlib.sha256(text.encode()).hexdigest()
        cache_key = f"embedding:{model}:{content_hash}"
        
        return await self.get(cache_key)

    async def set_embedding(
        self, 
        text: str, 
        embedding: List[float],
        model: str = "text-embedding-3-small"
    ) -> None:
        """Set embedding in cache with content validation."""
        content_hash = hashlib.sha256(text.encode()).hexdigest()
        cache_key = f"embedding:{model}:{content_hash}"
        
        await self.set(cache_key, embedding, ttl=86400)  # 24 hour TTL for embeddings


class CrawlResultCache(PerformanceCache):
    """Specialized cache for crawl results with URL-based invalidation."""
    
    async def get_crawl_result(self, url: str) -> Optional[Dict[str, Any]]:
        """Get crawl result from cache."""
        cache_key = f"crawl:{url}"
        return await self.get(cache_key)

    async def set_crawl_result(
        self, 
        url: str, 
        result: Dict[str, Any],
        ttl: float = 3600
    ) -> None:
        """Set crawl result in cache."""
        cache_key = f"crawl:{url}"
        await self.set(cache_key, result, ttl=ttl, persist_to_disk=True)


# Global cache instances
embedding_cache = EmbeddingCache(
    max_memory_size=200_000_000,  # 200MB for embeddings
    cache_dir=".cache/embeddings"
)

crawl_cache = CrawlResultCache(
    max_memory_size=300_000_000,  # 300MB for crawl results
    cache_dir=".cache/crawl_results"
)

query_cache = PerformanceCache(
    max_memory_size=100_000_000,  # 100MB for query results
    cache_dir=".cache/queries"
)