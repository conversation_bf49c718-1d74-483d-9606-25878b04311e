"""Graph database validation functionality."""
import os


def validate_graph_db_connection() -> bool:
    """Check if graph database environment variables are configured."""
    # Check if any graph database URI is configured
    graph_db_uri = os.getenv("GRAPH_DB_URI") or os.getenv("NEO4J_URI") or os.getenv("MEMGRAPH_URI")
    return bool(graph_db_uri)


def format_graph_db_error(error: Exception, db_type: str = "graph database") -> str:
    """Format graph database connection errors for user-friendly messages."""
    error_str = str(error).lower()
    
    if "authentication" in error_str or "unauthorized" in error_str:
        return f"{db_type} authentication failed. Check username and password."
    elif "connection" in error_str or "refused" in error_str or "timeout" in error_str:
        return f"Cannot connect to {db_type}. Check URI and ensure the database is running."
    elif "database" in error_str:
        return f"{db_type} database error. Check if the database exists and is accessible."
    else:
        return f"{db_type} error: {str(error)}"