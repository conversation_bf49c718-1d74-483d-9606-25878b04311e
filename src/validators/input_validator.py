"""
Input validation utilities for MCP tools.
Provides comprehensive validation for URLs, strings, and other user inputs.
"""

import re
import urllib.parse
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse


class InputValidator:
    """Comprehensive input validation for MCP tools."""
    
    # URL validation patterns
    ALLOWED_URL_SCHEMES = {'http', 'https'}
    DANGEROUS_URL_PATTERNS = [
        r'file://',
        r'ftp://',
        r'javascript:',
        r'data:',
        r'localhost',
        r'127\.0\.0\.1',
        r'10\.',
        r'192\.168\.',
        r'172\.(1[6-9]|2[0-9]|3[01])\.',
        r'0\.0\.0\.0',
        r'[::]',
        r'::1'
    ]
    
    # String validation limits
    MAX_URL_LENGTH = 2048
    MAX_QUERY_LENGTH = 1000
    MAX_SOURCE_LENGTH = 255
    MAX_MATCH_COUNT = 50
    
    @staticmethod
    def validate_url(url: str) -> Dict[str, Any]:
        """
        Validate URL for security and format.
        
        Args:
            url: URL to validate
            
        Returns:
            Dictionary with validation result
        """
        if not url or not isinstance(url, str):
            return {"valid": False, "error": "URL is required and must be a string"}
        
        # Check length
        if len(url) > InputValidator.MAX_URL_LENGTH:
            return {"valid": False, "error": f"URL too long (max {InputValidator.MAX_URL_LENGTH} chars)"}
        
        # Basic URL parsing
        try:
            parsed = urlparse(url)
        except Exception:
            return {"valid": False, "error": "Invalid URL format"}
        
        # Check scheme
        if parsed.scheme not in InputValidator.ALLOWED_URL_SCHEMES:
            return {"valid": False, "error": f"Unsupported URL scheme: {parsed.scheme}"}
        
        # Check for dangerous patterns
        for pattern in InputValidator.DANGEROUS_URL_PATTERNS:
            if re.search(pattern, url, re.IGNORECASE):
                return {"valid": False, "error": "URL contains potentially dangerous elements"}
        
        # Check for proper hostname
        if not parsed.netloc:
            return {"valid": False, "error": "URL must have a valid hostname"}
        
        # Additional hostname validation
        hostname = parsed.netloc.split(':')[0]  # Remove port if present
        if not hostname or hostname.startswith('.') or hostname.endswith('.'):
            return {"valid": False, "error": "Invalid hostname format"}
        
        return {"valid": True, "parsed": parsed}
    
    @staticmethod
    def validate_query_string(query: str) -> Dict[str, Any]:
        """
        Validate query string for searches.
        
        Args:
            query: Query string to validate
            
        Returns:
            Dictionary with validation result
        """
        if not query or not isinstance(query, str):
            return {"valid": False, "error": "Query is required and must be a string"}
        
        # Check length
        if len(query) > InputValidator.MAX_QUERY_LENGTH:
            return {"valid": False, "error": f"Query too long (max {InputValidator.MAX_QUERY_LENGTH} chars)"}
        
        # Check for SQL injection patterns
        dangerous_patterns = [
            r"';", r'";', r'--', r'/\*', r'\*/', r'xp_', r'sp_',
            r'union\s+select', r'drop\s+table', r'delete\s+from',
            r'insert\s+into', r'update\s+set', r'create\s+table',
            r'alter\s+table', r'exec\s*\(', r'execute\s*\(',
            r'<script', r'javascript:', r'vbscript:', r'onload=',
            r'onerror=', r'onclick=', r'<iframe', r'<object',
            r'<embed', r'<applet'
        ]
        
        query_lower = query.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                return {"valid": False, "error": "Query contains potentially dangerous content"}
        
        # Check for excessive special characters
        special_char_count = len(re.findall(r'[^\w\s\-_.,!?()[\]{}:;@#$%^&*+=|\\/<>~`]', query))
        if special_char_count > 20:
            return {"valid": False, "error": "Query contains too many special characters"}
        
        return {"valid": True, "sanitized": query.strip()}
    
    @staticmethod
    def validate_source_filter(source: Optional[str]) -> Dict[str, Any]:
        """
        Validate source filter parameter.
        
        Args:
            source: Source filter string
            
        Returns:
            Dictionary with validation result
        """
        if source is None:
            return {"valid": True, "sanitized": None}
        
        if not isinstance(source, str):
            return {"valid": False, "error": "Source filter must be a string"}
        
        # Check length
        if len(source) > InputValidator.MAX_SOURCE_LENGTH:
            return {"valid": False, "error": f"Source filter too long (max {InputValidator.MAX_SOURCE_LENGTH} chars)"}
        
        # Basic sanitization - remove potential dangerous characters
        sanitized = re.sub(r'[^\w\-._]', '', source)
        
        if not sanitized:
            return {"valid": False, "error": "Source filter contains no valid characters"}
        
        return {"valid": True, "sanitized": sanitized}
    
    @staticmethod
    def validate_match_count(match_count: int) -> Dict[str, Any]:
        """
        Validate match count parameter.
        
        Args:
            match_count: Number of matches to return
            
        Returns:
            Dictionary with validation result
        """
        if not isinstance(match_count, int):
            return {"valid": False, "error": "Match count must be an integer"}
        
        if match_count < 1:
            return {"valid": False, "error": "Match count must be at least 1"}
        
        if match_count > InputValidator.MAX_MATCH_COUNT:
            return {"valid": False, "error": f"Match count too high (max {InputValidator.MAX_MATCH_COUNT})"}
        
        return {"valid": True, "validated": match_count}
    
    @staticmethod
    def validate_chunk_size(chunk_size: int) -> Dict[str, Any]:
        """
        Validate chunk size parameter.
        
        Args:
            chunk_size: Size of content chunks
            
        Returns:
            Dictionary with validation result
        """
        if not isinstance(chunk_size, int):
            return {"valid": False, "error": "Chunk size must be an integer"}
        
        if chunk_size < 100:
            return {"valid": False, "error": "Chunk size must be at least 100 characters"}
        
        if chunk_size > 50000:
            return {"valid": False, "error": "Chunk size too large (max 50000 characters)"}
        
        return {"valid": True, "validated": chunk_size}
    
    @staticmethod
    def validate_max_depth(max_depth: int) -> Dict[str, Any]:
        """
        Validate max depth parameter for crawling.
        
        Args:
            max_depth: Maximum crawling depth
            
        Returns:
            Dictionary with validation result
        """
        if not isinstance(max_depth, int):
            return {"valid": False, "error": "Max depth must be an integer"}
        
        if max_depth < 1:
            return {"valid": False, "error": "Max depth must be at least 1"}
        
        if max_depth > 10:
            return {"valid": False, "error": "Max depth too high (max 10 for safety)"}
        
        return {"valid": True, "validated": max_depth}
    
    @staticmethod
    def validate_max_concurrent(max_concurrent: int) -> Dict[str, Any]:
        """
        Validate max concurrent parameter.
        
        Args:
            max_concurrent: Maximum concurrent operations
            
        Returns:
            Dictionary with validation result
        """
        if not isinstance(max_concurrent, int):
            return {"valid": False, "error": "Max concurrent must be an integer"}
        
        if max_concurrent < 1:
            return {"valid": False, "error": "Max concurrent must be at least 1"}
        
        if max_concurrent > 20:
            return {"valid": False, "error": "Max concurrent too high (max 20 for resource safety)"}
        
        return {"valid": True, "validated": max_concurrent}
    
    @staticmethod
    def validate_github_url(repo_url: str) -> Dict[str, Any]:
        """
        Validate GitHub repository URL.
        
        Args:
            repo_url: GitHub repository URL
            
        Returns:
            Dictionary with validation result
        """
        if not repo_url or not isinstance(repo_url, str):
            return {"valid": False, "error": "Repository URL is required"}
        
        repo_url = repo_url.strip()
        
        # Check length
        if len(repo_url) > InputValidator.MAX_URL_LENGTH:
            return {"valid": False, "error": f"URL too long (max {InputValidator.MAX_URL_LENGTH} chars)"}
        
        # Basic GitHub URL validation
        if not ("github.com" in repo_url.lower() or repo_url.endswith(".git")):
            return {"valid": False, "error": "Must be a valid GitHub repository URL"}
        
        # Check URL format
        if not (repo_url.startswith("https://") or repo_url.startswith("git@")):
            return {"valid": False, "error": "Repository URL must start with https:// or git@"}
        
        # Extract repository name
        try:
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            if not repo_name or not re.match(r'^[a-zA-Z0-9_.-]+$', repo_name):
                return {"valid": False, "error": "Invalid repository name format"}
        except Exception:
            return {"valid": False, "error": "Cannot extract repository name from URL"}
        
        return {"valid": True, "repo_name": repo_name, "sanitized_url": repo_url}


def validate_mcp_tool_input(tool_name: str, **kwargs) -> Dict[str, Any]:
    """
    Validate input for specific MCP tools.
    
    Args:
        tool_name: Name of the MCP tool
        **kwargs: Tool-specific parameters
        
    Returns:
        Dictionary with validation results
    """
    validator = InputValidator()
    results = {"valid": True, "errors": [], "validated_params": {}}
    
    if tool_name == "crawl_single_page":
        # Validate URL
        url_result = validator.validate_url(kwargs.get("url", ""))
        if not url_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"URL validation failed: {url_result['error']}")
        else:
            results["validated_params"]["url"] = kwargs["url"]
    
    elif tool_name == "smart_crawl_url":
        # Validate URL
        url_result = validator.validate_url(kwargs.get("url", ""))
        if not url_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"URL validation failed: {url_result['error']}")
        else:
            results["validated_params"]["url"] = kwargs["url"]
        
        # Validate max_depth
        depth_result = validator.validate_max_depth(kwargs.get("max_depth", 3))
        if not depth_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Max depth validation failed: {depth_result['error']}")
        else:
            results["validated_params"]["max_depth"] = depth_result["validated"]
        
        # Validate max_concurrent
        concurrent_result = validator.validate_max_concurrent(kwargs.get("max_concurrent", 5))
        if not concurrent_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Max concurrent validation failed: {concurrent_result['error']}")
        else:
            results["validated_params"]["max_concurrent"] = concurrent_result["validated"]
        
        # Validate chunk_size
        chunk_result = validator.validate_chunk_size(kwargs.get("chunk_size", 5000))
        if not chunk_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Chunk size validation failed: {chunk_result['error']}")
        else:
            results["validated_params"]["chunk_size"] = chunk_result["validated"]
    
    elif tool_name == "perform_rag_query":
        # Validate query
        query_result = validator.validate_query_string(kwargs.get("query", ""))
        if not query_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Query validation failed: {query_result['error']}")
        else:
            results["validated_params"]["query"] = query_result["sanitized"]
        
        # Validate source filter
        source_result = validator.validate_source_filter(kwargs.get("source"))
        if not source_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Source validation failed: {source_result['error']}")
        else:
            results["validated_params"]["source"] = source_result["sanitized"]
        
        # Validate match_count
        match_result = validator.validate_match_count(kwargs.get("match_count", 5))
        if not match_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Match count validation failed: {match_result['error']}")
        else:
            results["validated_params"]["match_count"] = match_result["validated"]
    
    elif tool_name == "search_code_examples":
        # Validate query
        query_result = validator.validate_query_string(kwargs.get("query", ""))
        if not query_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Query validation failed: {query_result['error']}")
        else:
            results["validated_params"]["query"] = query_result["sanitized"]
        
        # Validate source_id
        source_result = validator.validate_source_filter(kwargs.get("source_id"))
        if not source_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Source ID validation failed: {source_result['error']}")
        else:
            results["validated_params"]["source_id"] = source_result["sanitized"]
        
        # Validate match_count
        match_result = validator.validate_match_count(kwargs.get("match_count", 5))
        if not match_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Match count validation failed: {match_result['error']}")
        else:
            results["validated_params"]["match_count"] = match_result["validated"]
    
    elif tool_name == "parse_github_repository":
        # Validate GitHub URL
        repo_result = validator.validate_github_url(kwargs.get("repo_url", ""))
        if not repo_result["valid"]:
            results["valid"] = False
            results["errors"].append(f"Repository URL validation failed: {repo_result['error']}")
        else:
            results["validated_params"]["repo_url"] = repo_result["sanitized_url"]
            results["validated_params"]["repo_name"] = repo_result["repo_name"]
    
    return results