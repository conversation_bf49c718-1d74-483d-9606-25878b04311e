"""URL validation functionality."""
from typing import Dict, Any
from urllib.parse import urlparse


def validate_url(url: str) -> bool:
    """
    Validate a general URL.
    
    Args:
        url: URL to validate
        
    Returns:
        True if URL is valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_github_url(repo_url: str) -> Dict[str, Any]:
    """
    Validate GitHub repository URL.
    
    Args:
        repo_url: GitHub repository URL
        
    Returns:
        Dictionary with validation result
    """
    if not repo_url or not isinstance(repo_url, str):
        return {"valid": False, "error": "Repository URL is required"}
    
    if not repo_url.startswith(("https://github.com/", "http://github.com/", "**************:")):
        return {"valid": False, "error": "URL must be a valid GitHub repository URL"}
    
    if not repo_url.endswith(".git"):
        return {"valid": False, "error": "Repository URL must end with .git"}
    
    # Basic validation - could be enhanced with actual URL checking
    if "//" in repo_url and len(repo_url.split("/")) < 5:
        return {"valid": False, "error": "Invalid GitHub repository URL format"}
    
    return {"valid": True}