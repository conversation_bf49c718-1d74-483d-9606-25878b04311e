"""Script path validation functionality."""
import os
from typing import Dict, Any


def validate_script_path(script_path: str) -> Dict[str, Any]:
    """
    Validate script path and return error info if invalid.
    
    Args:
        script_path: Path to the script file
        
    Returns:
        Dictionary with validation result
    """
    if not script_path or not isinstance(script_path, str):
        return {"valid": False, "error": "Script path is required"}
    
    if not os.path.exists(script_path):
        return {"valid": False, "error": f"Script not found: {script_path}"}
    
    if not script_path.endswith('.py'):
        return {"valid": False, "error": "Only Python (.py) files are supported"}
    
    try:
        # Check if file is readable
        with open(script_path, 'r', encoding='utf-8') as f:
            f.read(1)  # Read first character to test
        return {"valid": True}
    except Exception as e:
        return {"valid": False, "error": f"Cannot read script file: {str(e)}"}