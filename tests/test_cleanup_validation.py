#!/usr/bin/env python3
"""
Comprehensive validation script to ensure all functionalities work after cleanup.

This script validates that the cleanup process hasn't broken any existing functionality
by testing all major components and integrations.
"""

import asyncio
import sys
import os
import importlib.util
from pathlib import Path
import json
from typing import Dict, Any, List

# Add src directory to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class CleanupValidator:
    """Validates that all functionality works after cleanup."""
    
    def __init__(self):
        self.results: Dict[str, Any] = {}
        self.failed_tests: List[str] = []
        self.passed_tests: List[str] = []
        
    def log_result(self, test_name: str, passed: bool, message: str = "") -> None:
        """Log test result."""
        self.results[test_name] = {
            "passed": passed,
            "message": message
        }
        
        if passed:
            self.passed_tests.append(test_name)
            print(f"✅ {test_name}: {message}")
        else:
            self.failed_tests.append(test_name)
            print(f"❌ {test_name}: {message}")
    
    def test_imports(self) -> None:
        """Test that all required modules can be imported."""
        print("\\n🔍 Testing imports...")
        
        # Test main module import
        try:
            import crawl4ai_mcp
            self.log_result("main_module_import", True, "Main module imports successfully")
        except Exception as e:
            self.log_result("main_module_import", False, f"Failed to import main module: {e}")
        
        # Test utils import
        try:
            import utils
            self.log_result("utils_import", True, "Utils module imports successfully")
        except Exception as e:
            self.log_result("utils_import", False, f"Failed to import utils: {e}")
        
        # Test performance optimizations import
        try:
            from performance import cache_manager, query_optimizer, monitor
            self.log_result("performance_import", True, "Performance modules import successfully")
        except Exception as e:
            self.log_result("performance_import", False, f"Failed to import performance modules: {e}")
        
        # Test security modules import
        try:
            from security import error_handler, rate_limiter
            self.log_result("security_import", True, "Security modules import successfully")
        except Exception as e:
            self.log_result("security_import", False, f"Failed to import security modules: {e}")
        
        # Test validators import
        try:
            from validators import input_validator, url_validator, script_validator
            self.log_result("validators_import", True, "Validator modules import successfully")
        except Exception as e:
            self.log_result("validators_import", False, f"Failed to import validators: {e}")
    
    def test_knowledge_graph_imports(self) -> None:
        """Test knowledge graph module imports."""
        print("\\n🔍 Testing knowledge graph imports...")
        
        try:
            # Add knowledge_graphs to path
            knowledge_graphs_path = project_root / 'knowledge_graphs'
            sys.path.insert(0, str(knowledge_graphs_path))
            
            import knowledge_graph_validator
            import parse_repo_into_neo4j
            import ai_script_analyzer
            import hallucination_reporter
            
            self.log_result("knowledge_graph_imports", True, "Knowledge graph modules import successfully")
        except Exception as e:
            self.log_result("knowledge_graph_imports", False, f"Failed to import knowledge graph modules: {e}")
    
    def test_environment_variables(self) -> None:
        """Test that environment variables are properly configured."""
        print("\\n🔍 Testing environment variables...")
        
        # Check if .env file exists
        env_file = project_root / '.env'
        if env_file.exists():
            self.log_result("env_file_exists", True, ".env file exists")
        else:
            self.log_result("env_file_exists", False, ".env file not found")
        
        # Test dotenv loading
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file, override=True)
            self.log_result("dotenv_loading", True, "Environment variables loaded successfully")
        except Exception as e:
            self.log_result("dotenv_loading", False, f"Failed to load environment variables: {e}")
    
    def test_fastmcp_initialization(self) -> None:
        """Test FastMCP server initialization."""
        print("\\n🔍 Testing FastMCP initialization...")
        
        try:
            from mcp.server.fastmcp import FastMCP
            
            # Test basic server creation
            mcp = FastMCP("test-server")
            self.log_result("fastmcp_creation", True, "FastMCP server created successfully")
            
            # Test tool decorator
            @mcp.tool()
            def test_tool(message: str = "test") -> str:
                return f"Tool response: {message}"
            
            self.log_result("tool_decorator", True, "Tool decorator works correctly")
            
        except Exception as e:
            self.log_result("fastmcp_initialization", False, f"Failed to initialize FastMCP: {e}")
    
    def test_supabase_client_creation(self) -> None:
        """Test Supabase client creation."""
        print("\\n🔍 Testing Supabase client creation...")
        
        try:
            from utils import get_supabase_client
            
            # This will fail if environment variables aren't set, but that's expected
            try:
                client = get_supabase_client()
                self.log_result("supabase_client", True, "Supabase client created successfully")
            except ValueError as e:
                if "SUPABASE_URL" in str(e) or "SUPABASE_SERVICE_KEY" in str(e):
                    self.log_result("supabase_client", True, "Supabase client function works (env vars not set)")
                else:
                    self.log_result("supabase_client", False, f"Unexpected error: {e}")
            except Exception as e:
                error_msg = str(e).lower()
                if "invalid url" in error_msg or "url" in error_msg:
                    self.log_result("supabase_client", True, "Supabase client function works (invalid URL in env)")
                else:
                    self.log_result("supabase_client", False, f"Unexpected error: {e}")
            
        except Exception as e:
            self.log_result("supabase_client", False, f"Failed to test Supabase client: {e}")
    
    def test_openai_configuration(self) -> None:
        """Test OpenAI configuration."""
        print("\\n🔍 Testing OpenAI configuration...")
        
        try:
            import openai
            
            # Test that OpenAI is properly configured
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key:
                self.log_result("openai_config", True, "OpenAI API key is configured")
            else:
                self.log_result("openai_config", True, "OpenAI module works (API key not set)")
            
        except Exception as e:
            self.log_result("openai_config", False, f"Failed to test OpenAI: {e}")
    
    def test_performance_optimizations(self) -> None:
        """Test performance optimization components."""
        print("\\n🔍 Testing performance optimizations...")
        
        try:
            from performance.cache_manager import PerformanceCache, EmbeddingCache
            from performance.query_optimizer import QueryOptimizer
            from performance.monitor import PerformanceMonitor
            
            # Test cache creation
            cache = PerformanceCache()
            self.log_result("cache_creation", True, "Performance cache created successfully")
            
            # Test monitor creation
            monitor = PerformanceMonitor()
            self.log_result("monitor_creation", True, "Performance monitor created successfully")
            
        except Exception as e:
            self.log_result("performance_optimizations", False, f"Failed to test performance optimizations: {e}")
    
    def test_security_components(self) -> None:
        """Test security components."""
        print("\\n🔍 Testing security components...")
        
        try:
            from security.error_handler import secure_error_handler
            from security.rate_limiter import RateLimiter
            
            # Test rate limiter creation
            rate_limiter = RateLimiter()
            self.log_result("rate_limiter", True, "Rate limiter created successfully")
            
            # Test error handler
            @secure_error_handler
            def test_function():
                return "success"
            
            result = test_function()
            self.log_result("error_handler", True, "Error handler decorator works correctly")
            
        except Exception as e:
            self.log_result("security_components", False, f"Failed to test security components: {e}")
    
    def test_validation_components(self) -> None:
        """Test validation components."""
        print("\\n🔍 Testing validation components...")
        
        try:
            from validators.input_validator import validate_mcp_tool_input
            from validators.url_validator import validate_url
            from validators.script_validator import validate_script_path
            
            # Test input validation
            result = validate_mcp_tool_input("test_tool", query="test query")
            self.log_result("input_validation", True, "Input validation works correctly")
            
            # Test URL validation
            is_valid = validate_url("https://example.com")
            self.log_result("url_validation", True, "URL validation works correctly")
            
        except Exception as e:
            self.log_result("validation_components", False, f"Failed to test validation components: {e}")
    
    def test_file_structure(self) -> None:
        """Test that the file structure is correct."""
        print("\\n🔍 Testing file structure...")
        
        expected_files = [
            "src/crawl4ai_mcp.py",
            "src/utils.py",
            "src/performance/__init__.py",
            "src/performance/cache_manager.py",
            "src/performance/query_optimizer.py",
            "src/performance/monitor.py",
            "src/security/error_handler.py",
            "src/security/rate_limiter.py",
            "src/validators/input_validator.py",
            "src/validators/url_validator.py",
            "src/validators/script_validator.py",
            "knowledge_graphs/knowledge_graph_validator.py",
            "knowledge_graphs/parse_repo_into_neo4j.py",
            "knowledge_graphs/ai_script_analyzer.py",
            "knowledge_graphs/hallucination_reporter.py",
            "tests/__init__.py",
            "docs/README.md",
            ".gitignore",
            "README.md",
            "CLAUDE.md",
            "pyproject.toml",
            "docker-compose.yml",
            "crawled_pages.sql"
        ]
        
        missing_files = []
        for file_path in expected_files:
            full_path = project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.log_result("file_structure", False, f"Missing files: {missing_files}")
        else:
            self.log_result("file_structure", True, "All expected files are present")
    
    def test_removed_files(self) -> None:
        """Test that unused files have been removed."""
        print("\\n🔍 Testing removed files...")
        
        should_not_exist = [
            "src/main.py",
            "src/config/",
            "src/database/",
            "src/embeddings/",
            "src/server/",
            "src/tools/",
            "src/crawlers/",
            "src/crawl4ai_mcp.egg-info/",
            "knowledge_graphs/analysis/",
            "knowledge_graphs/database/",
            "knowledge_graphs/reporting/",
            "knowledge_graphs/validation/",
            "test_comprehensive_fixes.py",
            "test_crawl_fix.py",
            "knowledge_graphs/test_script.py"
        ]
        
        existing_files = []
        for file_path in should_not_exist:
            full_path = project_root / file_path
            if full_path.exists():
                existing_files.append(file_path)
        
        if existing_files:
            self.log_result("removed_files", False, f"Files that should be removed still exist: {existing_files}")
        else:
            self.log_result("removed_files", True, "All unused files have been properly removed")
    
    async def run_all_tests(self) -> None:
        """Run all validation tests."""
        print("🚀 Starting comprehensive cleanup validation...")
        print("=" * 60)
        
        # Run all tests
        self.test_imports()
        self.test_knowledge_graph_imports()
        self.test_environment_variables()
        self.test_fastmcp_initialization()
        self.test_supabase_client_creation()
        self.test_openai_configuration()
        self.test_performance_optimizations()
        self.test_security_components()
        self.test_validation_components()
        self.test_file_structure()
        self.test_removed_files()
        
        # Print summary
        print("\\n" + "=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_count = len(self.passed_tests)
        failed_count = len(self.failed_tests)
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_count}")
        print(f"Failed: {failed_count}")
        print(f"Success rate: {(passed_count/total_tests)*100:.1f}%")
        
        if failed_count > 0:
            print(f"\\n❌ Failed tests:")
            for test in self.failed_tests:
                print(f"  - {test}: {self.results[test]['message']}")
        
        print(f"\\n✅ Passed tests:")
        for test in self.passed_tests:
            print(f"  - {test}")
        
        # Final verdict
        if failed_count == 0:
            print("\\n🎉 ALL TESTS PASSED! Cleanup was successful.")
            print("✅ All functionalities are working correctly after cleanup.")
        else:
            print(f"\\n⚠️  {failed_count} tests failed. Please review the issues above.")
        
        return failed_count == 0


async def main():
    """Main function to run validation."""
    validator = CleanupValidator()
    success = await validator.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())