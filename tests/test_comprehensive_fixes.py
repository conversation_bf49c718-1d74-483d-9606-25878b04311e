#!/usr/bin/env python3
"""
Comprehensive test suite to verify all fixes are working correctly.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>, BrowserConfig, CrawlerRunConfig
from utils import _enhance_code_query, _is_meaningful_code_snippet, extract_code_blocks

async def test_browser_config_fix():
    """Test 1: Verify BrowserConfig chrome_flags fix"""
    print("🔧 Testing BrowserConfig fix...")
    
    try:
        # Test the fixed BrowserConfig
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            text_mode=True,
            extra_args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-javascript",
                "--memory-pressure-off",
                "--max_old_space_size=512"
            ]
        )
        
        crawler = AsyncWebCrawler(config=browser_config)
        
        async with crawler:
            # Test basic crawling
            result = await crawler.arun("https://httpbin.org/html")
            
            if result.success:
                print("✅ BrowserConfig fix verified - crawling works")
                return True
            else:
                print(f"❌ BrowserConfig test failed: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"❌ BrowserConfig test failed with exception: {e}")
        return False

def test_code_search_enhancements():
    """Test 2: Verify code search query enhancement"""
    print("\n🔍 Testing code search enhancements...")
    
    try:
        # Test query enhancement
        queries = [
            "python api",
            "javascript async",
            "sql database",
            "docker container",
            "embedding generation"
        ]
        
        for query in queries:
            enhanced = _enhance_code_query(query)
            if "Technical context:" in enhanced:
                print(f"✅ Query enhancement working for: {query}")
            else:
                print(f"⚠️ Query enhancement minimal for: {query}")
        
        # Test meaningful code snippet detection
        test_snippets = [
            "def hello_world():\n    print('Hello, World!')",
            "const api = async () => { return await fetch('/api') }",
            "SELECT * FROM users WHERE id = 1",
            "just some text",
            "import requests\nresponse = requests.get('https://api.example.com')"
        ]
        
        meaningful_count = 0
        for snippet in test_snippets:
            if _is_meaningful_code_snippet(snippet):
                meaningful_count += 1
        
        print(f"✅ Meaningful code detection: {meaningful_count}/{len(test_snippets)} snippets recognized")
        return True
        
    except Exception as e:
        print(f"❌ Code search enhancement test failed: {e}")
        return False

def test_code_extraction_improvements():
    """Test 3: Verify improved code extraction"""
    print("\n📋 Testing code extraction improvements...")
    
    try:
        # Test markdown with various code blocks
        markdown_content = '''
# API Documentation

Here's a simple Python function:

```python
def get_user(user_id):
    return User.objects.get(id=user_id)
```

And here's a JavaScript example:

```javascript
async function fetchUser(id) {
    const response = await fetch(`/api/users/${id}`);
    return response.json();
}
```

Also some SQL:

```sql
SELECT name, email FROM users WHERE active = true;
```

And a short bash command:

```bash
curl -X GET https://api.example.com/users
```
'''
        
        # Test with lower minimum length
        code_blocks = extract_code_blocks(markdown_content, min_length=10)
        
        print(f"✅ Extracted {len(code_blocks)} code blocks")
        
        # Verify we're catching smaller meaningful snippets
        languages_found = set()
        for block in code_blocks:
            if block['language']:
                languages_found.add(block['language'])
        
        print(f"✅ Languages detected: {', '.join(languages_found)}")
        
        return len(code_blocks) >= 3  # Should find at least 3 blocks
        
    except Exception as e:
        print(f"❌ Code extraction test failed: {e}")
        return False

async def test_error_handling():
    """Test 4: Verify improved error handling"""
    print("\n🚨 Testing error handling improvements...")
    
    try:
        # Test with an invalid URL to trigger error handling
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            text_mode=True,
            extra_args=["--no-sandbox", "--disable-dev-shm-usage"]
        )
        
        crawler = AsyncWebCrawler(config=browser_config)
        
        async with crawler:
            # Test with invalid URL
            try:
                result = await crawler.arun("https://this-domain-does-not-exist-12345.com")
                if not result.success:
                    print("✅ Error handling working - graceful failure for invalid URL")
                    return True
                else:
                    print("⚠️ Unexpected success with invalid URL")
                    return False
            except Exception as e:
                print(f"✅ Error handling working - caught exception: {type(e).__name__}")
                return True
                
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_technical_keyword_mapping():
    """Test 5: Verify technical keyword mapping"""
    print("\n🔤 Testing technical keyword mapping...")
    
    try:
        # Test various technical terms
        test_cases = [
            ("python api", ["REST API", "endpoint", "HTTP request"]),
            ("javascript async", ["asynchronous", "await", "Promise"]),
            ("database sql", ["SQL", "query", "CRUD"]),
            ("docker container", ["containerization", "deployment", "DevOps"]),
            ("machine learning", ["AI", "neural network", "training"])
        ]
        
        success_count = 0
        for query, expected_terms in test_cases:
            enhanced = _enhance_code_query(query)
            
            # Check if at least one expected term is in the enhanced query
            found_terms = [term for term in expected_terms if term in enhanced]
            if found_terms:
                print(f"✅ Technical mapping working for '{query}': found {found_terms}")
                success_count += 1
            else:
                print(f"❌ Technical mapping failed for '{query}'")
        
        return success_count >= len(test_cases) * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"❌ Technical keyword mapping test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Running comprehensive fix verification tests...\n")
    
    tests = [
        ("BrowserConfig Fix", test_browser_config_fix()),
        ("Code Search Enhancement", test_code_search_enhancements()),
        ("Code Extraction", test_code_extraction_improvements()),
        ("Error Handling", test_error_handling()),
        ("Technical Keyword Mapping", test_technical_keyword_mapping())
    ]
    
    results = []
    for test_name, test_func in tests:
        if asyncio.iscoroutine(test_func):
            result = await test_func
        else:
            result = test_func
        results.append((test_name, result))
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All fixes verified successfully!")
        print("The MCP Crawl4AI RAG server is ready for deployment.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)