version: '3.8'

services:
  crawl4ai-rag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        PORT: 8051
    container_name: crawl4ai-rag-mcp-server
    ports:
      - "8051:8051"
    environment:
      # Server configuration
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
      
      # API Keys (set these in your .env file)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4o-mini}
      
      # RAG Strategy flags
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-true}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      
      # Supabase configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # Graph Database (disabled for now, prepared for Memgraph)
      - GRAPH_DB_URI=${GRAPH_DB_URI:-}
      - GRAPH_DB_USER=${GRAPH_DB_USER:-}
      - GRAPH_DB_PASSWORD=${GRAPH_DB_PASSWORD:-}
      
      # Future Memgraph configuration
      - MEMGRAPH_URI=${MEMGRAPH_URI:-}
      - MEMGRAPH_USER=${MEMGRAPH_USER:-}
      - MEMGRAPH_PASSWORD=${MEMGRAPH_PASSWORD:-}
    
    env_file:
      - .env
    
    volumes:
      # Only mount cache directories, not source code
      - crawl4ai_cache:/app/.crawl4ai_cache
    
    networks:
      - mcp-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "--max-time", "3", "-I", "http://localhost:8051/sse"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  crawl4ai_cache:
    driver: local

networks:
  mcp-network:
    driver: bridge