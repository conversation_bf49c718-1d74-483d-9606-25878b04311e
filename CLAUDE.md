# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Model Context Protocol (MCP) server that integrates Crawl4AI web crawling with Supabase vector database for RAG (Retrieval Augmented Generation) capabilities. The server enables AI agents to crawl websites, store content in vector databases, and perform semantic search over crawled content.

### Core Architecture

- **MCP Server**: Built using FastMCP framework (`src/crawl4ai_mcp.py`)
- **Utilities**: Core functionality in `src/utils.py` for Supabase integration and embeddings
- **Knowledge Graph**: Optional AI hallucination detection using Neo4j (`knowledge_graphs/` folder)
- **Database**: PostgreSQL with pgvector extension via Supabase
- **Embeddings**: OpenAI text-embedding-3-small model (1536 dimensions)

## Development Commands

### Installation and Setup
```bash
# Install dependencies with uv
uv pip install -e .
crawl4ai-setup

# Or with <PERSON>er
docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .
```

### Running the Server
```bash
# Direct execution
uv run src/crawl4ai_mcp.py

# With Docker
docker run --env-file .env -p 8051:8051 mcp/crawl4ai-rag
```

### Database Setup
Execute `crawled_pages.sql` in Supabase SQL Editor to create required tables and pgvector extension.

### Testing Knowledge Graph Features
```bash
# Test hallucination detection (requires Neo4j setup)
python knowledge_graphs/ai_hallucination_detector.py [script_path]

# Test knowledge graph queries
python knowledge_graphs/query_knowledge_graph.py
```

## Key Components

### MCP Tools Available
1. **`crawl_single_page`**: Crawl and store a single webpage
2. **`smart_crawl_url`**: Intelligent crawling based on URL type (sitemap, txt, webpage)
3. **`get_available_sources`**: List all crawled domains
4. **`perform_rag_query`**: Semantic search with optional source filtering
5. **`search_code_examples`**: Search for code examples (when `USE_AGENTIC_RAG=true`)
6. **`parse_github_repository`**: Add GitHub repo to knowledge graph (when `USE_KNOWLEDGE_GRAPH=true`)
7. **`check_ai_script_hallucinations`**: Validate AI-generated code against knowledge graph
8. **`query_knowledge_graph`**: Explore Neo4j knowledge graph

### RAG Strategy Flags
Configure in `.env` file (all default to `false`):
- **`USE_CONTEXTUAL_EMBEDDINGS`**: Enhanced chunk context via LLM
- **`USE_HYBRID_SEARCH`**: Combines vector + keyword search
- **`USE_AGENTIC_RAG`**: Specialized code example extraction
- **`USE_RERANKING`**: Cross-encoder result reranking
- **`USE_KNOWLEDGE_GRAPH`**: Neo4j-based hallucination detection

### Database Schema
- **`sources`**: Tracks crawled domains with summaries
- **`crawled_pages`**: Main content chunks with vector embeddings
- **`code_examples`**: Extracted code blocks with summaries (when agentic RAG enabled)

## Configuration Requirements

### Required Environment Variables
```bash
# MCP Server
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse  # or stdio

# APIs
OPENAI_API_KEY=your_key
MODEL_CHOICE=gpt-4o-mini  # for summaries/contextual embeddings

# Supabase
SUPABASE_URL=your_url
SUPABASE_SERVICE_KEY=your_key

# Neo4j (optional, for knowledge graph)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
```

## Knowledge Graph Architecture

When `USE_KNOWLEDGE_GRAPH=true`:
- **Nodes**: Repository, File, Class, Method, Function, Attribute
- **Relationships**: CONTAINS, DEFINES, HAS_METHOD, HAS_ATTRIBUTE
- **Purpose**: Validate AI-generated code against real repository structures
- **Note**: Not fully Docker-compatible yet; use direct `uv` execution

### Key Knowledge Graph Files
- `parse_repo_into_neo4j.py`: Repository analysis and Neo4j insertion
- `ai_script_analyzer.py`: AST-based Python code analysis
- `knowledge_graph_validator.py`: Hallucination detection logic
- `hallucination_reporter.py`: Report generation for validation results

## Development Notes

### Content Processing Pipeline
1. URL analysis → appropriate crawler selection
2. Content chunking by headers and size (default 5000 chars)
3. Optional contextual embedding enhancement
4. Vector embedding generation (OpenAI)
5. Supabase storage with metadata
6. Optional code example extraction and separate storage

### Search Process
1. Query embedding generation
2. Vector similarity search (+ optional keyword search if hybrid)
3. Optional cross-encoder reranking
4. Source filtering if specified
5. Results with similarity scores and metadata

### Integration with MCP Clients
- **SSE Transport**: `http://localhost:8051/sse`
- **Stdio Transport**: Direct Python execution
- **Docker Considerations**: Use `host.docker.internal` for client access

## Recommended Development Workflow

1. Set up Supabase database with `crawled_pages.sql`
2. Configure `.env` with required variables
3. Start with basic RAG (`USE_HYBRID_SEARCH=true`, `USE_RERANKING=true`)
4. Add agentic features for code examples if needed
5. Enable knowledge graph for hallucination detection (requires Neo4j setup)
6. Test with various URL types (sitemaps, documentation, individual pages)

## Current Context

- **Current Timeline**: You are now in 2025 July

## Web Search Guidelines

- All web searches shall be used with Brave Search MCP