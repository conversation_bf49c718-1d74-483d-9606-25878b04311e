# Deployment Guide - MCP Crawl4AI RAG Server

## Fixed Issues Summary

### 1. Browser Configuration Issues ✅ FIXED
- **Problem**: `BrowserConfig.__init__() got an unexpected keyword argument 'chrome_flags'`
- **Solution**: Updated to use `extra_args` parameter instead of `chrome_flags`
- **Changes Made**:
  - Updated `BrowserConfig` in `src/crawl4ai_mcp.py` line 175
  - Added `text_mode=True` for better performance
  - Updated dependencies to use `crawl4ai>=0.7.0`

### 2. Code Example Search Improvements ✅ ENHANCED
- **Problem**: Poor relevance for technical queries
- **Solution**: Enhanced query processing with technical keyword mappings
- **Changes Made**:
  - Added `_enhance_code_query()` function with technical mappings
  - Improved `_is_meaningful_code_snippet()` to capture smaller but relevant code
  - Reduced minimum code block length from 1000 to 50 characters
  - Added programming language context detection

### 3. Error Handling and Fallback Strategies ✅ IMPROVED
- **Problem**: Poor error messages and no fallback mechanisms
- **Solution**: Added comprehensive error handling with fallback configurations
- **Changes Made**:
  - Added `format_crawl_error()` function for user-friendly error messages
  - Implemented fallback crawling configurations
  - Added retry logic with simplified configs

## Installation Steps

### 1. Install Dependencies
```bash
# Install with uv (recommended)
uv pip install -e .

# Or install with pip
pip install -e .
```

### 2. Setup Crawl4AI
```bash
# Run the setup (may require manual Playwright installation)
uv run crawl4ai-setup

# If Playwright installation fails, run manually:
python -m playwright install
```

### 3. Environment Configuration
Create a `.env` file in the project root:

```env
# MCP Server Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse

# Required API Keys
OPENAI_API_KEY=your_openai_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Optional: Model Configuration
MODEL_CHOICE=gpt-4o-mini

# RAG Enhancement Flags (all optional, default: false)
USE_CONTEXTUAL_EMBEDDINGS=false
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=true
USE_RERANKING=true
USE_KNOWLEDGE_GRAPH=false

# Optional: Neo4j Configuration (for knowledge graph features)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password
```

### 4. Database Setup
Execute the SQL script in your Supabase SQL Editor:
```sql
-- See crawled_pages.sql for full schema
```

## Running the Server

### Development Mode
```bash
# Direct execution with stdio transport
uv run python src/crawl4ai_mcp.py

# With SSE transport (default)
TRANSPORT=sse uv run python src/crawl4ai_mcp.py
```

### Production Mode with Docker
```bash
# Build the image
docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .

# Run with environment file
docker run --env-file .env -p 8051:8051 mcp/crawl4ai-rag
```

## Testing the Fixes

### 1. Test Browser Configuration
```bash
# Run the test script
uv run python test_crawl_fix.py
```

### 2. Test MCP Tools
```bash
# Test single page crawling
curl -X POST http://localhost:8051/mcp/tools/crawl_single_page \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# Test smart crawling
curl -X POST http://localhost:8051/mcp/tools/smart_crawl_url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/sitemap.xml"}'
```

## Key Improvements Made

### Browser Configuration
- Fixed `chrome_flags` → `extra_args` compatibility issue
- Added `text_mode=True` for faster crawling
- Optimized Chrome flags for memory usage

### Code Search Enhancement
- Technical keyword mappings for better relevance
- Programming language context detection
- Improved small code snippet detection
- Enhanced query processing with synonyms

### Error Handling
- User-friendly error messages
- Fallback crawling configurations
- Comprehensive exception handling
- Detailed error reporting

## Performance Optimizations

### Memory Management
- Browser session pooling
- Garbage collection optimization
- Memory monitoring and cleanup
- Resource-limited browser configuration

### Crawling Efficiency
- Concurrent crawling with semaphores
- Batch processing for multiple URLs
- Intelligent retry mechanisms
- Optimized browser settings

## Troubleshooting

### Common Issues

1. **Playwright Installation Failed**
   ```bash
   # Try manual installation
   python -m playwright install
   # Or with system packages
   python -m playwright install --with-deps
   ```

2. **Browser Launch Errors**
   - Check if required system libraries are installed
   - Verify browser binaries are properly installed
   - Try running with `--no-sandbox` flag

3. **Memory Issues**
   - Reduce `max_concurrent` parameter
   - Enable memory monitoring
   - Use `text_mode=True` for lighter crawling

4. **Database Connection Issues**
   - Verify Supabase credentials
   - Check network connectivity
   - Ensure database schema is properly set up

## Next Steps

1. **Content Diversity**: Add more technical documentation sources
2. **Query Expansion**: Implement advanced query understanding
3. **Performance Monitoring**: Add query analytics and metrics
4. **Advanced Features**: Enhance knowledge graph capabilities

## Support

For issues related to:
- **Crawl4AI**: Check the official documentation at https://docs.crawl4ai.com
- **MCP Protocol**: Refer to the MCP specification
- **This Implementation**: Check the GitHub repository issues