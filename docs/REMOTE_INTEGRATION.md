# Remote Claude Desktop Integration Guide

## ✅ Docker Deployment Status

The Crawl4AI MCP server is now successfully deployed in Docker and accessible at:
- **Local URL**: `http://localhost:8051/sse`
- **Status**: ✅ Running successfully and healthy
- **Transport**: SSE (Server-Sent Events)
- **Health Check**: ✅ Passing with proper curl-based monitoring

## 🚀 Remote Integration Options

### Option 1: <PERSON> (Recommended for Development)

#### Add Remote MCP Server
```bash
claude mcp add --transport sse crawl4ai-rag http://YOUR_SERVER_IP:8051/sse
```

#### Test Connection
```bash
claude mcp list
claude mcp test crawl4ai-rag
```

### Option 2: <PERSON> - Official Integrations (Recommended for Production)

1. **Open Claude Desktop**
2. **Go to Settings > Integrations**
3. **Add Integration:**
   - **Name**: Crawl4AI RAG MCP
   - **URL**: `http://YOUR_SERVER_IP:8051/sse`
   - **Description**: Web crawling and RAG capabilities

### Option 3: <PERSON> - JSON Configuration

**IMPORTANT**: <PERSON> uses command-based configuration, not direct URL connections. Use `mcp-remote` package for remote servers.

#### Configuration File Location

**macOS:**
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Windows:**
```
%APPDATA%\Claude\claude_desktop_config.json
```

**Linux:**
```
~/.config/Claude/claude_desktop_config.json
```

#### For Local Testing (localhost)
```json
{
  "mcpServers": {
    "crawl4ai-rag-local": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8051/sse"
      ]
    }
  }
}
```

#### For Remote Connection (HTTP)
```json
{
  "mcpServers": {
    "crawl4ai-rag-remote": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://YOUR_SERVER_IP:8051/sse"
      ]
    }
  }
}
```

#### For HTTPS/Production
```json
{
  "mcpServers": {
    "crawl4ai-rag-prod": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://your-domain.com/sse"
      ]
    }
  }
}
```

#### Complete Example Configuration
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://YOUR_SERVER_IP:8051/sse"
      ]
    },
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Documents"
      ]
    }
  }
}
```

## 🔧 Deployment Commands

### Development Deployment (with source mounting)
```bash
docker-compose up -d
```

### Production Deployment (recommended)
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Check Status
```bash
docker-compose -f docker-compose.prod.yml ps
docker-compose -f docker-compose.prod.yml logs
```

### Stop Deployment
```bash
docker-compose -f docker-compose.prod.yml down
```

## 🌐 Remote Deployment Steps

### 1. Prepare Remote Server

#### Update .env for Remote Access
```bash
# Ensure these settings in .env
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse
```

#### Open Firewall Port
```bash
# Ubuntu/Debian
sudo ufw allow 8051/tcp

# Or restrict to specific IPs
sudo ufw allow from YOUR_CLIENT_IP to any port 8051
```

### 2. Deploy on Remote Server
```bash
# Clone repository
git clone https://github.com/your-repo/mcp-crawl4ai-rag.git
cd mcp-crawl4ai-rag

# Configure environment
cp .env.example .env
# Edit .env with your API keys and settings

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Test Remote Connectivity
```bash
# From your local machine
curl -I http://YOUR_SERVER_IP:8051/sse
```

## 🛠️ Available MCP Tools

Once connected, you'll have access to:

### Core Crawling Tools
- `crawl_single_page` - Crawl and store a single webpage
- `smart_crawl_url` - Intelligent crawling based on URL type
- `get_available_sources` - List all crawled domains

### RAG Query Tools
- `perform_rag_query` - Semantic search over crawled content
- `search_code_examples` - Search for code examples (when agentic RAG enabled)

### Knowledge Graph Tools (when enabled)
- `parse_github_repository` - Add GitHub repo to knowledge graph
- `check_ai_script_hallucinations` - Validate AI-generated code
- `query_knowledge_graph` - Explore the knowledge graph

## 🔒 Security Considerations

### For Production Deployment

1. **Use HTTPS with SSL/TLS**
2. **Implement authentication/API keys**
3. **Restrict firewall access to specific IPs**
4. **Use reverse proxy (nginx) for better security**

### HTTPS Setup with nginx

#### Install nginx and SSL
```bash
# Install nginx and certbot
sudo apt install nginx certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com
```

#### nginx Configuration for HTTPS
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:8051;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE specific headers
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Production Docker Compose with nginx
```yaml
version: '3.8'
services:
  crawl4ai-rag-mcp:
    build: .
    container_name: crawl4ai-rag-mcp-prod
    ports:
      - "8051:8051"
    environment:
      - TRANSPORT=sse
      - HOST=0.0.0.0
      - PORT=8051
    env_file:
      - .env
    volumes:
      - crawl4ai_cache:/app/.crawl4ai_cache
    restart: unless-stopped
    
  # Optional: nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - crawl4ai-rag-mcp
    restart: unless-stopped

volumes:
  crawl4ai_cache:
```

## 🧪 Testing Your Deployment

### 1. Health Check
```bash
curl -f http://YOUR_SERVER_IP:8051/sse
```

### 2. Test with Claude Code
```bash
# Add the server
claude mcp add --transport sse crawl4ai-rag http://YOUR_SERVER_IP:8051/sse

# Test basic functionality
claude mcp test crawl4ai-rag

# Try a tool
claude "Use crawl4ai-rag to get available sources"
```

### 3. Test with Claude Desktop

After adding the JSON configuration:

1. **Save the configuration file**
2. **Restart Claude Desktop completely**
3. **Look for MCP tools button** (bottom right of prompt input)
4. **Test with**: "Please list the available crawling sources"

## 🚨 Troubleshooting

### Claude Desktop Connection Issues

#### Common Error: "Non-HTTPS URLs are only allowed for localhost"
**Solution**: Add `--allow-http` flag to args array:
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "--allow-http",
        "http://YOUR_SERVER_IP:8051/sse"
      ]
    }
  }
}
```

#### Server Connection Failed
1. **Check server is running**: `docker-compose -f docker-compose.prod.yml ps`
2. **Test endpoint directly**: `curl -I http://YOUR_SERVER_IP:8051/sse`
3. **Verify firewall**: `sudo ufw status`
4. **Check Claude Desktop logs**: Look for MCP connection errors
5. **Restart Claude Desktop**: Complete restart after config changes

#### MCP Tools Not Appearing
1. **Verify JSON syntax**: Use `python -m json.tool claude_desktop_config.json`
2. **Check file location**: Ensure config is in correct directory
3. **Restart Claude Desktop**: Required after configuration changes
4. **Look for error messages**: Check Claude Desktop's developer console

### Server Issues
- ✅ Server running: `docker-compose -f docker-compose.prod.yml ps`
- ✅ Port accessible: `curl -I http://YOUR_SERVER_IP:8051/sse`
- ✅ Firewall open: `sudo ufw status`
- ✅ Correct URL format: Must include `/sse` endpoint
- ✅ Health check passing: Container shows `(healthy)` status

### Performance Issues
- Monitor: `docker stats crawl4ai-rag-mcp-server`
- Logs: `docker-compose -f docker-compose.prod.yml logs -f`
- Restart: `docker-compose -f docker-compose.prod.yml restart`

### Debug Commands
```bash
# Check container health
docker inspect crawl4ai-rag-mcp-server --format='{{json .State.Health}}'

# Test mcp-remote directly
npx mcp-remote --allow-http http://YOUR_SERVER_IP:8051/sse

# Validate JSON config
python -m json.tool ~/.claude/claude_desktop_config.json
```

## 📊 Resource Usage

- **Memory**: ~2-4GB (depends on embeddings cache)
- **CPU**: Moderate (spikes during crawling/embedding)
- **Storage**: Variable (depends on crawled content)
- **Network**: Outbound for API calls and crawling

## 🔄 Updates and Maintenance

### Update Deployment
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose -f docker-compose.prod.yml up --build -d
```

### Backup Data
```bash
# Backup cache volume
docker run --rm -v mcp-crawl4ai-rag_crawl4ai_cache:/data -v $(pwd):/backup ubuntu tar czf /backup/crawl4ai_cache_backup.tar.gz /data
```

### Monitoring
```bash
# Monitor container resources
docker stats crawl4ai-rag-mcp-server --no-stream

# Check logs for errors
docker-compose -f docker-compose.prod.yml logs --tail=100 -f

# Health status
docker-compose -f docker-compose.prod.yml ps
```

## 🎯 Quick Setup Summary

### For Remote HTTP Server
1. **Deploy server**: `docker-compose -f docker-compose.prod.yml up -d`
2. **Configure Claude Desktop**:
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag": {
         "command": "npx",
         "args": [
           "mcp-remote",
           "--allow-http",
           "http://YOUR_SERVER_IP:8051/sse"
         ]
       }
     }
   }
   ```
3. **Restart Claude Desktop**
4. **Test**: "Please list available crawling sources"

### For HTTPS Production
1. **Set up domain and SSL**
2. **Configure nginx reverse proxy**
3. **Configure Claude Desktop**:
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag": {
         "command": "npx",
         "args": [
           "mcp-remote",
           "https://your-domain.com/sse"
         ]
       }
     }
   }
   ```

---

## 🎯 Next Steps

1. **Deploy to your remote server** using the production docker-compose
2. **Configure Claude Desktop** with the correct JSON syntax
3. **Test the integration** with a simple crawling task
4. **Set up HTTPS and security** for production use

Your Crawl4AI MCP server is now ready for remote Claude Desktop integration! 🚀