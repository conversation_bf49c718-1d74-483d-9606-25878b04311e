# Remote MCP Server Setup Quick Guide

This guide provides step-by-step instructions for setting up the Crawl4AI RAG MCP server for remote access.

## Quick Setup for Different Use Cases

### 1. <PERSON> Code Remote Connection (Easiest)

**Setup:**
1. Deploy server with Docker Compose:
   ```bash
   cp .env.example .env
   # Configure .env with your credentials
   docker-compose up -d
   ```

2. Connect from <PERSON> Code:
   ```bash
   claude mcp add --transport sse crawl4ai-rag http://YOUR_SERVER_IP:8051/sse
   ```

**Best for:** Developers who primarily use Claude Code

### 2. Claude Des<PERSON>op Official Integration (Recommended for Production)

**Setup:**
1. Deploy server (same as above)
2. In <PERSON>: Settings > Integrations > Add Integration
3. Enter URL: `http://YOUR_SERVER_IP:8051/sse`

**Best for:** End users who want the official supported method

### 3. <PERSON> with Supergateway (Development/Advanced)

**Setup:**
1. Deploy server (same as above)
2. Install Supergateway:
   ```bash
   npm install -g @supercorp-ai/supergateway
   ```
3. Add to <PERSON> config:
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-remote": {
         "command": "supergateway",
         "args": ["--sse-to-stdio", "http://YOUR_SERVER_IP:8051/sse"]
       }
     }
   }
   ```

**Best for:** Developers who need JSON configuration control

### 4. Production HTTPS Setup

**Setup:**
1. Use production Docker Compose:
   ```bash
   cp configs/docker-compose.prod.yml docker-compose.yml
   cp configs/nginx.prod.conf nginx.conf
   # Configure SSL certificates
   docker-compose up -d
   ```

2. Update Claude connections to use HTTPS:
   ```bash
   claude mcp add --transport sse crawl4ai-rag https://your-domain.com/sse
   ```

**Best for:** Production deployments with proper SSL/security

## Configuration Files Reference

- `claude-code-config.json` - Claude Code configuration
- `claude-desktop-config.json` - Claude Desktop local configuration  
- `claude-desktop-docker-config.json` - Claude Desktop with Docker
- `docker-compose.prod.yml` - Production deployment
- `nginx.prod.conf` - Nginx reverse proxy configuration
- `docker-compose.gateway.yml` - Supergateway service

## Common IP Replacements

Replace `YOUR_SERVER_IP` with:
- **Local testing:** `localhost` or `127.0.0.1`
- **LAN access:** Your machine's local IP (e.g., `*************`)
- **Cloud deployment:** Your server's public IP or domain name
- **Docker internal:** `host.docker.internal` (for client in Docker)

## Security Checklist

- [ ] Configure firewall to only allow necessary ports
- [ ] Use HTTPS for production deployments
- [ ] Set up proper SSL certificates (Let's Encrypt recommended)
- [ ] Configure rate limiting in nginx
- [ ] Monitor server logs for unusual activity
- [ ] Keep Docker images updated
- [ ] Use strong passwords in .env file

## Testing Your Setup

1. **Test server connectivity:**
   ```bash
   curl http://YOUR_SERVER_IP:8051/health
   ```

2. **Test SSE endpoint:**
   ```bash
   curl -H "Accept: text/event-stream" http://YOUR_SERVER_IP:8051/sse
   ```

3. **Test Claude connection:**
   - Try a simple query in Claude to see if MCP tools are available
   - Check for any error messages in Claude or server logs

## Troubleshooting

If connections fail:

1. **Check server status:** `docker-compose ps`
2. **Check server logs:** `docker-compose logs`
3. **Test network connectivity:** `ping YOUR_SERVER_IP`
4. **Check firewall:** `sudo ufw status`
5. **Verify port is open:** `netstat -tlnp | grep 8051`

For detailed troubleshooting, see the main SETUP_GUIDE.md file.

## Support

- Main documentation: `configs/SETUP_GUIDE.md`
- Development guide: `CLAUDE.md`
- Project README: `README.md`