# Security Guide - MCP Crawl4AI RAG Server

## Overview

This document outlines the security measures implemented in the MCP Crawl4AI RAG server and provides guidelines for secure deployment and operation.

## Security Measures Implemented

### 1. Authentication & Authorization
- **Environment Variables**: All API keys and credentials are stored in environment variables
- **No Hardcoded Secrets**: All hardcoded credentials have been removed
- **Input Validation**: Comprehensive validation for all user inputs
- **Rate Limiting**: Per-client and global rate limiting to prevent abuse

### 2. Network Security
- **Host Binding**: Default binding to localhost (127.0.0.1) instead of all interfaces
- **CORS Configuration**: Restricted CORS origins (no wildcards)
- **SSL/TLS**: Modern TLS configuration with strong cipher suites
- **Security Headers**: Comprehensive security headers including CSP, HSTS, etc.

### 3. Input Validation & Sanitization
- **URL Validation**: Strict URL validation to prevent SSRF attacks
- **Query Sanitization**: Cypher query validation to prevent injection attacks
- **Path Validation**: Secure file path validation to prevent directory traversal
- **Length Limits**: Maximum length restrictions on all inputs

### 4. Error Handling
- **Secure Error Messages**: Generic error messages that don't reveal system internals
- **Detailed Logging**: Comprehensive server-side logging for debugging
- **Error Classification**: Severity-based error classification and handling

### 5. Container Security
- **Non-root User**: Application runs as non-root user in container
- **Security Options**: Container hardening with security options
- **Resource Limits**: Memory and CPU limits to prevent resource exhaustion
- **Health Checks**: Container health monitoring

### 6. Application Security
- **Dependency Management**: Regular dependency updates and vulnerability scanning
- **Code Quality**: Input validation, error handling, and secure coding practices
- **Concurrency Limits**: Limits on concurrent operations to prevent DoS

## Configuration Guidelines

### Environment Variables
```bash
# Use strong, unique passwords
NEO4J_PASSWORD=your-secure-password-here

# Use environment-specific API keys
OPENAI_API_KEY=your-openai-api-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Secure host binding
HOST=127.0.0.1  # For development
HOST=0.0.0.0    # For Docker (with proper firewall rules)
```

### Production Deployment
1. **Use HTTPS**: Always deploy with SSL/TLS certificates
2. **Firewall Rules**: Implement proper firewall rules
3. **Regular Updates**: Keep dependencies and base images updated
4. **Monitoring**: Implement security monitoring and alerting
5. **Backup**: Regular backups of configuration and data

### Docker Security
```yaml
# Bind to localhost only
ports:
  - "127.0.0.1:8051:8051"

# Security configurations
security_opt:
  - no-new-privileges:true
cap_drop:
  - ALL
mem_limit: 2g
cpus: 1.0
```

## Security Best Practices

### 1. Secrets Management
- Use a secrets management service (HashiCorp Vault, AWS Secrets Manager)
- Rotate API keys regularly
- Never commit secrets to version control
- Use different credentials for different environments

### 2. Network Security
- Deploy behind a reverse proxy (nginx, traefik)
- Use Web Application Firewall (WAF)
- Implement IP whitelisting where appropriate
- Monitor for suspicious network activity

### 3. Application Security
- Regular security audits and penetration testing
- Code review for security issues
- Static analysis tools (bandit, semgrep)
- Dependency vulnerability scanning

### 4. Monitoring & Logging
- Centralized logging with security event monitoring
- Alerting for suspicious activities
- Regular log analysis and review
- Audit trails for administrative actions

## Security Testing

### Static Analysis
```bash
# Install security tools
pip install bandit safety

# Run security checks
bandit -r src/
safety check
```

### Dependency Scanning
```bash
# Check for known vulnerabilities
pip-audit

# Update dependencies
pip-compile --upgrade
```

### Container Security
```bash
# Scan container images
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image crawl4ai-rag:latest
```

## Incident Response

### 1. Detection
- Monitor logs for security events
- Set up alerts for suspicious activities
- Regular security assessments

### 2. Response
- Isolate affected systems
- Assess the scope of the incident
- Implement containment measures
- Document all actions taken

### 3. Recovery
- Restore from clean backups
- Apply security patches
- Update security measures
- Conduct post-incident review

## Compliance Considerations

### Data Protection
- Encrypt sensitive data at rest and in transit
- Implement data retention policies
- Regular data backups and testing
- Access controls and audit logging

### Regulatory Compliance
- GDPR: Data protection and privacy measures
- SOC 2: Security, availability, and confidentiality controls
- HIPAA: If handling healthcare data
- PCI DSS: If processing payment data

## Security Contacts

For security issues or questions:
- Email: <EMAIL>
- Bug Bounty: [Your bug bounty program]
- Security Advisory: [Your security advisory process]

## Updates and Maintenance

- Regular security reviews (quarterly)
- Dependency updates (monthly)
- Security patches (as needed)
- Documentation updates (as needed)

---

**Last Updated**: 2025-01-15  
**Version**: 1.0  
**Review Cycle**: Quarterly