# MCP Crawl4AI RAG Server Documentation

This directory contains comprehensive documentation for the MCP Crawl4AI RAG server.

## Documentation Index

### Core Documentation

- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Complete deployment instructions for production environments
- **[Remote Integration Guide](REMOTE_INTEGRATION.md)** - How to integrate with remote MCP clients
- **[Security Guide](SECURITY.md)** - Security best practices and security measures
- **[Setup Guide](SETUP_GUIDE.md)** - Quick setup instructions for development
- **[Remote Setup Guide](README-Remote-Setup.md)** - Remote configuration setup

### Quick Start

1. **Prerequisites**: Python 3.12+, Docker, Supabase account, OpenAI API key
2. **Installation**: Clone repository and set up environment variables
3. **Configuration**: Create `.env` file with required settings
4. **Running**: Use Docker or run directly with `uv`

### Key Features

- **Smart URL Detection**: Automatically handles different URL types
- **Recursive Crawling**: Follows internal links intelligently
- **Vector Search**: Semantic search over crawled content
- **RAG Strategies**: Multiple optimization strategies available
- **Knowledge Graph**: AI hallucination detection with Neo4j
- **Performance Optimization**: Intelligent caching and query optimization

### Architecture Overview

```
MCP Client (Claude, Windsurf, etc.)
         \u2193
    MCP Protocol (stdio/sse)
         \u2193
   Crawl4AI MCP Server
         \u2193
[Crawl4AI] [Supabase] [OpenAI] [Neo4j]
```

### RAG Strategies

The server supports multiple RAG strategies that can be enabled independently:

1. **Contextual Embeddings** - Enhanced context for better retrieval
2. **Hybrid Search** - Combines vector and keyword search
3. **Agentic RAG** - Specialized code example extraction
4. **Reranking** - Improved result relevance
5. **Knowledge Graph** - AI hallucination detection

### Performance Features

- **Intelligent Caching**: Multi-level caching system
- **Database Optimization**: Connection pooling and query optimization
- **Memory Management**: Adaptive memory monitoring and cleanup
- **Real-time Monitoring**: Performance metrics and bottleneck detection

### Security Features

- **Input Validation**: Comprehensive validation for all inputs
- **Rate Limiting**: Per-client and global rate limiting
- **Error Handling**: Secure error handling without information disclosure
- **Security Headers**: Comprehensive security headers and SSL/TLS
- **Container Security**: Hardened Docker configuration

### Getting Help

- Check the main [README.md](../README.md) for overview and quick start
- Review specific guides in this directory for detailed instructions
- Check the [CLAUDE.md](../CLAUDE.md) file for development guidelines
- For issues, refer to the GitHub repository

### Contributing

This project is designed as a testbed for integration with Archon. While contributions are welcome, the primary focus is on building the foundation for Archon V2's knowledge engine capabilities.

### License

This project is licensed under the MIT License. See [LICENSE](../LICENSE) for details.