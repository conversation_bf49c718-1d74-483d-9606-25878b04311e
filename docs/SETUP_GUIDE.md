# Crawl4AI RAG MCP Server Setup Guide

This guide provides comprehensive instructions for setting up and running the refactored, modular Crawl4AI RAG MCP server.

## Prerequisites

1. **Python 3.12+** (if running locally)
2. **Docker & Docker Compose** (for containerized deployment)
3. **Supabase Account** (for vector database)
4. **OpenAI API Key** (for embeddings and LLM)

## Environment Setup

1. **Copy the environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Configure your `.env` file:**
   ```bash
   # Required settings
   OPENAI_API_KEY=your_openai_api_key_here
   MODEL_CHOICE=gpt-4o-mini
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_SERVICE_KEY=your_supabase_service_key
   
   # Recommended settings for basic usage
   USE_HYBRID_SEARCH=true
   USE_RERANKING=true
   USE_CONTEXTUAL_EMBEDDINGS=false
   USE_AGENTIC_RAG=false
   USE_KNOWLEDGE_GRAPH=false
   ```

3. **Set up Supabase database:**
   - Run the SQL in `crawled_pages.sql` in your Supabase SQL Editor
   - This creates the required tables and functions

## Deployment Options

### Option 1: Docker Compose (Recommended)

1. **Build and start the service:**
   ```bash
   docker-compose up --build -d
   ```

2. **Check logs:**
   ```bash
   docker-compose logs -f crawl4ai-rag-mcp
   ```

3. **Stop the service:**
   ```bash
   docker-compose down
   ```

The server will be available at `http://localhost:8051` with SSE transport.

### Option 2: Local Development

1. **Install dependencies:**
   ```bash
   uv pip install -e .
   crawl4ai-setup
   ```

2. **Run the server:**
   ```bash
   # For SSE transport (web-based)
   TRANSPORT=sse uv run src/main.py
   
   # For stdio transport (direct connection)
   TRANSPORT=stdio uv run src/main.py
   ```

### Option 3: Docker Build for Claude Desktop

1. **Build the Docker image:**
   ```bash
   docker build -t crawl4ai-rag-mcp .
   ```

2. **Test the container:**
   ```bash
   docker run --rm --env-file .env -e TRANSPORT=stdio crawl4ai-rag-mcp
   ```

## Claude Integration

### Claude Code Configuration

#### Local Connection (Server running on same machine)

1. **Command line method:**
   ```bash
   claude mcp add-json crawl4ai-rag '{"type":"http","url":"http://localhost:8051/sse"}' --scope user
   ```

2. **JSON configuration method:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag": {
         "transport": "sse",
         "url": "http://localhost:8051/sse"
       }
     }
   }
   ```

#### Remote Connection (Server running on different machine)

1. **Command line method:**
   ```bash
   claude mcp add --transport sse crawl4ai-rag http://YOUR_SERVER_IP:8051/sse
   ```

2. **JSON configuration method:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-remote": {
         "transport": "sse",
         "url": "http://YOUR_SERVER_IP:8051/sse"
       }
     }
   }
   ```

3. **HTTPS/Production configuration:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-prod": {
         "transport": "sse",
         "url": "https://your-domain.com/sse"
       }
     }
   }
   ```

### Claude Desktop Configuration

Claude Desktop has different approaches for local vs remote connections:

#### Local Connection Options

##### Option A: Direct Python Execution
```json
{
  "mcpServers": {
    "crawl4ai-rag": {
      "command": "python",
      "args": ["/path/to/mcp-crawl4ai-rag/src/main.py"],
      "env": {
        "TRANSPORT": "stdio",
        "OPENAI_API_KEY": "your_key",
        "SUPABASE_URL": "your_url",
        "SUPABASE_SERVICE_KEY": "your_key",
        "USE_HYBRID_SEARCH": "true",
        "USE_RERANKING": "true"
      }
    }
  }
}
```

##### Option B: Docker Execution
```json
{
  "mcpServers": {
    "crawl4ai-rag-docker": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i",
        "--env-file", "/path/to/mcp-crawl4ai-rag/.env",
        "-e", "TRANSPORT=stdio",
        "crawl4ai-rag-mcp"
      ]
    }
  }
}
```

#### Remote Connection Options

##### Option A: Official Integrations (Recommended for Production)

Claude Desktop now supports remote MCP servers via **Settings > Integrations**:

1. **Open Claude Desktop**
2. **Go to Settings > Integrations**
3. **Add Integration:**
   - **Name**: Crawl4AI RAG MCP
   - **URL**: `http://YOUR_SERVER_IP:8051/sse`
   - **Description**: Web crawling and RAG capabilities

**Note**: This method doesn't require JSON configuration and is the official approach for remote servers.

##### Option B: Supergateway Bridge (Recommended for Development)

For JSON-based remote connections, use **Supergateway** as a transport bridge:

1. **Install Supergateway:**
   ```bash
   npm install -g @supercorp-ai/supergateway
   ```

2. **Run Supergateway (SSE to stdio bridge):**
   ```bash
   supergateway --sse-to-stdio http://YOUR_SERVER_IP:8051/sse
   ```

3. **Claude Desktop configuration:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-remote": {
         "command": "supergateway",
         "args": ["--sse-to-stdio", "http://YOUR_SERVER_IP:8051/sse"]
       }
     }
   }
   ```

##### Option C: Docker-based Supergateway

For a more robust setup, run Supergateway in Docker:

1. **Create a docker-compose-gateway.yml:**
   ```yaml
   version: '3.8'
   services:
     supergateway:
       image: supercorpai/supergateway:latest
       command: ["--sse-to-stdio", "http://YOUR_SERVER_IP:8051/sse"]
       stdin_open: true
       tty: true
   ```

2. **Claude Desktop configuration:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-gateway": {
         "command": "docker",
         "args": [
           "compose", "-f", "/path/to/docker-compose-gateway.yml", 
           "run", "--rm", "supergateway"
         ]
       }
     }
   }
   ```

## Available MCP Tools

The refactored server provides the following tools:

### Core Crawling Tools
- `crawl_single_page` - Crawl a single webpage
- `smart_crawl_url` - Intelligent crawling based on URL type
- `get_available_sources` - List all crawled domains

### RAG Query Tools
- `perform_rag_query` - Semantic search over crawled content
- `search_code_examples` - Search for code examples (when agentic RAG enabled)

### Knowledge Graph Tools (when enabled)
- `parse_github_repository` - Add GitHub repo to knowledge graph
- `check_ai_script_hallucinations` - Validate AI-generated code
- `query_knowledge_graph` - Explore the knowledge graph

## Configuration Options

### RAG Strategy Flags

- **`USE_HYBRID_SEARCH`** (recommended: `true`)
  - Combines vector similarity with keyword search
  - Better results for technical queries

- **`USE_RERANKING`** (recommended: `true`)
  - Improves result relevance using cross-encoder
  - Minimal performance impact

- **`USE_CONTEXTUAL_EMBEDDINGS`** (default: `false`)
  - Enhances chunks with contextual information
  - Slower indexing but better retrieval accuracy
  - Requires additional LLM API calls

- **`USE_AGENTIC_RAG`** (default: `false`)
  - Extracts and summarizes code examples
  - Provides specialized code search tool
  - Significantly slower crawling

- **`USE_KNOWLEDGE_GRAPH`** (default: `false`)
  - Currently disabled (preparing for Memgraph migration)
  - Will enable AI hallucination detection

## Remote Deployment and Production Setup

### Docker Compose for Remote Access

For remote deployment, create a production-ready Docker Compose configuration:

1. **Create docker-compose.prod.yml:**
   ```yaml
   version: '3.8'
   
   services:
     crawl4ai-rag-mcp:
       build:
         context: .
         dockerfile: Dockerfile
         args:
           PORT: 8051
       container_name: crawl4ai-rag-mcp-prod
       ports:
         - "8051:8051"
       environment:
         - TRANSPORT=sse
         - HOST=0.0.0.0
         - PORT=8051
       env_file:
         - .env
       volumes:
         - crawl4ai_cache:/app/.crawl4ai_cache
       networks:
         - mcp-network
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
         interval: 30s
         timeout: 10s
         retries: 3
         start_period: 40s
       deploy:
         resources:
           limits:
             memory: 2G
             cpus: '1.0'
   
     # Optional: Reverse proxy for HTTPS
     nginx:
       image: nginx:alpine
       container_name: crawl4ai-nginx
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - crawl4ai-rag-mcp
       networks:
         - mcp-network
       restart: unless-stopped
   
   volumes:
     crawl4ai_cache:
       driver: local
   
   networks:
     mcp-network:
       driver: bridge
   ```

2. **Create nginx.conf for HTTPS:**
   ```nginx
   events {
       worker_connections 1024;
   }
   
   http {
       upstream mcp_backend {
           server crawl4ai-rag-mcp:8051;
       }
   
       server {
           listen 80;
           server_name your-domain.com;
           return 301 https://$server_name$request_uri;
       }
   
       server {
           listen 443 ssl;
           server_name your-domain.com;
   
           ssl_certificate /etc/nginx/ssl/cert.pem;
           ssl_certificate_key /etc/nginx/ssl/key.pem;
   
           location / {
               proxy_pass http://mcp_backend;
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
               
               # SSE specific headers
               proxy_buffering off;
               proxy_cache off;
               proxy_set_header Connection '';
               proxy_http_version 1.1;
               chunked_transfer_encoding off;
           }
       }
   }
   ```

### Network and Security Considerations

#### Firewall Configuration

1. **Open required ports:**
   ```bash
   # For HTTP access
   sudo ufw allow 8051/tcp
   
   # For HTTPS production setup
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   ```

2. **Restrict access to specific IPs (recommended):**
   ```bash
   # Allow only specific client IPs
   sudo ufw allow from YOUR_CLIENT_IP to any port 8051
   ```

#### SSL/TLS Setup for Production

1. **Generate SSL certificates:**
   ```bash
   # Using Let's Encrypt (recommended)
   sudo apt install certbot
   sudo certbot certonly --standalone -d your-domain.com
   
   # Copy certificates for nginx
   sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./ssl/cert.pem
   sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./ssl/key.pem
   ```

2. **Update Claude configurations to use HTTPS:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-prod": {
         "transport": "sse",
         "url": "https://your-domain.com/sse"
       }
     }
   }
   ```

#### Authentication and Access Control

For production deployments, consider adding authentication:

1. **Environment variable for API key:**
   ```bash
   # Add to .env
   MCP_API_KEY=your_secure_api_key_here
   ```

2. **nginx basic auth example:**
   ```nginx
   location / {
       auth_basic "MCP Server Access";
       auth_basic_user_file /etc/nginx/.htpasswd;
       proxy_pass http://mcp_backend;
       # ... other proxy settings
   }
   ```

### Cloud Deployment Examples

#### AWS EC2 Deployment

1. **Launch EC2 instance (t3.medium recommended)**
2. **Install Docker and Docker Compose**
3. **Clone repository and configure:**
   ```bash
   git clone https://github.com/your-repo/mcp-crawl4ai-rag.git
   cd mcp-crawl4ai-rag
   cp .env.example .env
   # Configure .env with your settings
   ```

4. **Deploy with production compose:**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

5. **Configure security group:** Allow ports 80, 443, and optionally 8051

#### Digital Ocean Droplet Deployment

1. **Create Ubuntu droplet (2GB RAM minimum)**
2. **Follow same Docker setup as AWS**
3. **Configure domain DNS:** Point A record to droplet IP
4. **Set up SSL with Let's Encrypt**

## Database Migration Preparation

The refactored codebase is prepared for easy migration from Neo4j to Memgraph:

- **Current State:** Neo4j support disabled, graph database abstraction layer created
- **Future:** Memgraph support will be added via the same interface
- **Benefits:** Better performance, simpler deployment, improved compatibility

To enable Memgraph in the future:
1. Update environment variables (MEMGRAPH_URI, MEMGRAPH_USER, MEMGRAPH_PASSWORD)
2. Uncomment Memgraph service in docker-compose.yml
3. Set USE_KNOWLEDGE_GRAPH=true

## Troubleshooting

### Common Issues

1. **"Module not found" errors:**
   - Ensure you've run `crawl4ai-setup` after installation
   - Check that all dependencies are installed with `uv pip install -e .`

2. **Database connection errors:**
   - Verify your Supabase credentials
   - Ensure the database schema has been created

3. **Docker issues:**
   - Check that the .env file is properly configured
   - Ensure Docker has sufficient memory (recommend 4GB+)

4. **MCP connection issues:**
   - Verify the server is running on the correct port
   - Check transport type matches your configuration
   - For Claude Desktop, ensure file paths are absolute

### Remote Connection Troubleshooting

#### Network Connectivity Issues

1. **Test server accessibility:**
   ```bash
   # Test basic HTTP connectivity
   curl http://YOUR_SERVER_IP:8051/health
   
   # Test SSE endpoint
   curl -H "Accept: text/event-stream" http://YOUR_SERVER_IP:8051/sse
   ```

2. **Check firewall settings:**
   ```bash
   # Check if port is open
   sudo ufw status
   netstat -tlnp | grep 8051
   
   # Test from client machine
   telnet YOUR_SERVER_IP 8051
   ```

3. **Verify Docker container status:**
   ```bash
   docker-compose logs crawl4ai-rag-mcp
   docker-compose ps
   ```

#### Claude Code Remote Connection Issues

1. **Invalid URL format:**
   ```bash
   # Correct format
   claude mcp add --transport sse crawl4ai-rag http://YOUR_SERVER_IP:8051/sse
   
   # Common mistakes to avoid
   # ❌ Missing /sse endpoint: http://YOUR_SERVER_IP:8051
   # ❌ Wrong transport: --transport stdio
   # ❌ Missing protocol: YOUR_SERVER_IP:8051/sse
   ```

2. **Connection timeout:**
   - Check if server is actually running: `docker-compose ps`
   - Verify network reachability: `ping YOUR_SERVER_IP`
   - Check for proxy/VPN interference

3. **Authentication errors:**
   - Ensure no authentication is required or properly configured
   - Check Claude Code logs for specific error messages

#### Claude Desktop Remote Connection Issues

##### Official Integrations Method

1. **Integration not appearing:**
   - Ensure you're using the latest Claude Desktop version
   - Restart Claude Desktop after adding integration
   - Check Settings > Integrations for error messages

2. **Connection failed errors:**
   - Verify the URL format: `http://YOUR_SERVER_IP:8051/sse`
   - Test the URL in a browser or with curl
   - Check server logs for incoming connection attempts

##### Supergateway Method

1. **Supergateway installation issues:**
   ```bash
   # Check Node.js version (requires Node 16+)
   node --version
   
   # Install with verbose logging
   npm install -g @supercorp-ai/supergateway --verbose
   
   # Verify installation
   supergateway --version
   ```

2. **Supergateway connection issues:**
   ```bash
   # Test Supergateway directly
   supergateway --sse-to-stdio http://YOUR_SERVER_IP:8051/sse
   
   # Check for error messages in the output
   # Common issues:
   # - Network unreachable: Check firewall/network
   # - SSE connection failed: Verify server endpoint
   # - Permission denied: Check file permissions
   ```

3. **Claude Desktop + Supergateway configuration:**
   ```json
   {
     "mcpServers": {
       "crawl4ai-rag-remote": {
         "command": "supergateway",
         "args": ["--sse-to-stdio", "http://YOUR_SERVER_IP:8051/sse"],
         "env": {}
       }
     }
   }
   ```

#### Docker Remote Deployment Issues

1. **Container not accessible externally:**
   ```bash
   # Check port binding
   docker-compose ps
   
   # Verify host binding in docker-compose.yml
   # Should be: "8051:8051" not "127.0.0.1:8051:8051"
   ```

2. **SSL/HTTPS issues:**
   ```bash
   # Check SSL certificate validity
   openssl x509 -in ./ssl/cert.pem -text -noout
   
   # Test SSL connection
   curl -k https://your-domain.com/sse
   ```

3. **Health check failures:**
   ```bash
   # Check container health
   docker inspect --format='{{.State.Health.Status}}' crawl4ai-rag-mcp-prod
   
   # Test health endpoint manually
   docker exec crawl4ai-rag-mcp-prod curl -f http://localhost:8051/health
   ```

#### Performance and Scaling Issues

1. **High memory usage:**
   - Monitor with: `docker stats`
   - Increase memory limits in docker-compose.yml
   - Consider enabling swap if needed

2. **Slow response times:**
   - Check server logs for processing time
   - Monitor network latency: `ping -c 10 YOUR_SERVER_IP`
   - Consider geographic proximity for better performance

3. **Connection timeouts:**
   - Increase timeout settings in nginx.conf
   - Check for resource exhaustion on server
   - Monitor concurrent connection limits

#### Debug Commands

1. **Comprehensive server check:**
   ```bash
   # Check all components
   docker-compose logs --tail=50 crawl4ai-rag-mcp
   curl -v http://YOUR_SERVER_IP:8051/health
   netstat -tlnp | grep 8051
   docker stats crawl4ai-rag-mcp --no-stream
   ```

2. **Network troubleshooting:**
   ```bash
   # Trace route to server
   traceroute YOUR_SERVER_IP
   
   # Check DNS resolution
   nslookup your-domain.com
   
   # Test specific port connectivity
   nc -zv YOUR_SERVER_IP 8051
   ```

3. **Claude Desktop debug:**
   ```bash
   # Check Claude Desktop logs (macOS)
   tail -f ~/Library/Logs/Claude/claude.log
   
   # Validate configuration file
   python -m json.tool ~/.claude/claude_desktop_config.json
   ```

### Performance Optimization

- **For large-scale crawling:** Enable hybrid search and reranking
- **For code-heavy documentation:** Enable agentic RAG
- **For maximum accuracy:** Enable contextual embeddings (slower)
- **For production:** Use Docker Compose with resource limits

## Support

For issues and questions:
1. Check the main README.md for general information
2. Review CLAUDE.md for development guidance
3. Examine the modular code structure in `src/` directories
4. Test with provided configuration examples

The refactored codebase is designed for maintainability, testability, and easy future enhancements.