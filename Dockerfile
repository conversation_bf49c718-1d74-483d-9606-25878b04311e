FROM python:3.12-slim

ARG PORT=8051

# Set up working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN pip install uv

# Copy the entire project
COPY . .

# Install dependencies and setup crawl4ai
RUN uv pip install --system -e . && \
    crawl4ai-setup

# Create required cache directories
RUN mkdir -p .cache/embeddings .cache/crawl_results .cache/queries

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE ${PORT}

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Command to run the MCP server
CMD ["python", "src/crawl4ai_mcp.py"]