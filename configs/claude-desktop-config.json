{"mcpServers": {"crawl4ai-rag": {"command": "python", "args": ["/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/main.py"], "env": {"TRANSPORT": "stdio", "OPENAI_API_KEY": "your_openai_api_key", "MODEL_CHOICE": "gpt-4o-mini", "USE_HYBRID_SEARCH": "true", "USE_RERANKING": "true", "USE_CONTEXTUAL_EMBEDDINGS": "false", "USE_AGENTIC_RAG": "false", "USE_KNOWLEDGE_GRAPH": "false", "SUPABASE_URL": "your_supabase_url", "SUPABASE_SERVICE_KEY": "your_supabase_service_key"}}}}