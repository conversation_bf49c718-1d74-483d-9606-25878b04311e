services:
  crawl4ai-rag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        PORT: 8051
    container_name: crawl4ai-rag-mcp-server
    ports:
      - "127.0.0.1:8051:8051"  # Bind to localhost only for security
    environment:
      # Server configuration
      - TRANSPORT=sse
      - HOST=0.0.0.0  # Inside container, but bound to localhost externally
      - PORT=8051
      
      # API Keys (set these in your .env file)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4o-mini}
      
      # RAG Strategy flags
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-true}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      
      # Supabase configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # Graph Database (disabled for now, prepared for Memgraph)
      - GRAPH_DB_URI=${GRAPH_DB_URI:-}
      - GRAPH_DB_USER=${GRAPH_DB_USER:-}
      - GRAPH_DB_PASSWORD=${GRAPH_DB_PASSWORD:-}
      
      # Future Memgraph configuration
      - MEMGRAPH_URI=${MEMGRAPH_URI:-}
      - MEMGRAPH_USER=${MEMGRAPH_USER:-}
      - MEMGRAPH_PASSWORD=${MEMGRAPH_PASSWORD:-}
    
    env_file:
      - .env
    
    volumes:
      # Cache directories for faster rebuilds
      - crawl4ai_cache:/app/.crawl4ai_cache
      # Note: Removed development mount to avoid permission issues
      # - .:/app
    
    networks:
      - mcp-network
    
    restart: unless-stopped
    
    # Security configurations
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE  # Only if binding to privileged ports
    read_only: false  # Application needs to write logs and temporary files
    tmpfs:
      - /run
      - /var/run
    ulimits:
      nofile:
        soft: 1024
        hard: 1024
      nproc:
        soft: 64
        hard: 64
    mem_limit: 2g
    cpus: 1.0
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Future: Memgraph database service (when we migrate from Neo4j)
  # memgraph:
  #   image: memgraph/memgraph:latest
  #   container_name: memgraph-db
  #   ports:
  #     - "7687:7687"
  #     - "7444:7444"
  #   environment:
  #     - MEMGRAPH_USER=${MEMGRAPH_USER:-memgraph}
  #     - MEMGRAPH_PASSWORD=${MEMGRAPH_PASSWORD:-memgraph}
  #   volumes:
  #     - memgraph_data:/var/lib/memgraph
  #     - memgraph_log:/var/log/memgraph
  #     - memgraph_etc:/etc/memgraph
  #   networks:
  #     - mcp-network
  #   restart: unless-stopped

volumes:
  crawl4ai_cache:
    driver: local
  pip_cache:
    driver: local
  # memgraph_data:
  #   driver: local
  # memgraph_log:
  #   driver: local
  # memgraph_etc:
  #   driver: local

networks:
  mcp-network:
    driver: bridge